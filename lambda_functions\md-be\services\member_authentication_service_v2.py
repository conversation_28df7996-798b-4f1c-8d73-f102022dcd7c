import logging
import requests
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from jose import jwt
from auth0.management import Auth0
from auth0_manage_api.auth0_manage import get_management_token
from models.member import Co<PERSON><PERSON>ber, CoAuth0User
from schemas.member import CoMemberCreateEnhanced
from config import settings
from utils.response_utils import created_response, success_response
from typing import Dict, Any, Optional
import secrets
import string

logger = logging.getLogger(__name__)

class MemberAuthenticationServiceV2:
    """
    Enhanced Member Authentication Service implementing the complete OAuth2/Auth0 flow
    Based on the provided authentication flow diagram
    """
    
    def __init__(self):
        self.auth0_domain = settings.DOMAIN
        self.client_id = settings.CLIENT_ID
        self.client_secret = settings.CLIENT_SECRET
        self.audience = settings.AUDIENCE
        self.redirect_uri = settings.REDIRECT_URI
        self.scope = settings.SCOPE

    # ==================== FRONTEND INTERACTION ====================
    
    def initiate_authentication(self, provider: str = "auth0", redirect_uri: Optional[str] = None) -> Dict[str, Any]:
        """
        Step 1: User starts authentication - Frontend redirects to Auth0 login
        """
        try:
            base_url = f"https://{self.auth0_domain}/authorize"
            final_redirect_uri = redirect_uri or self.redirect_uri
            
            params = {
                "client_id": self.client_id,
                "response_type": "code",
                "redirect_uri": final_redirect_uri,
                "scope": self.scope,
                "audience": self.audience,
                "state": self._generate_state()  # CSRF protection
            }

            # Add connection parameter for social providers
            if provider.lower() == 'google':
                params["connection"] = "google-oauth2"
            elif provider.lower() == 'apple':
                params["connection"] = "apple"
            elif provider.lower() == 'linkedin':
                params["connection"] = "linkedin"
            elif provider.lower() == 'username-password':
                params["connection"] = "Username-Password-Authentication"

            auth_url = f"{base_url}?" + "&".join([f"{k}={v}" for k, v in params.items()])
            
            return success_response(
                "Authentication URL generated successfully",
                {
                    "auth_url": auth_url,
                    "state": params["state"]
                }
            )
            
        except Exception as e:
            logger.exception("Error initiating authentication")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to initiate authentication",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

    # ==================== AUTH0 PROCESSING ====================
    
    def handle_auth0_callback(self, authorization_code: str, state: str, db: Session) -> Dict[str, Any]:
        """
        Step 2-4: Handle Auth0 callback, exchange code for tokens, and process user
        """
        try:
            # Validate state parameter (CSRF protection)
            if not self._validate_state(state):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "message": "Invalid state parameter",
                        "status_code": status.HTTP_400_BAD_REQUEST
                    }
                )

            # Exchange authorization code for tokens
            tokens = self._exchange_code_for_tokens(authorization_code)
            
            # Extract and verify user information from tokens
            user_info = self._extract_user_info_from_tokens(tokens)
            
            # Check if user exists or create new user
            member = self._get_or_create_member(user_info, db)
            
            return success_response(
                "Authentication successful",
                {
                    "member": {
                        "uuid": str(member.uuid),
                        "email": member.loginEmail,
                        "auth0_id": member.auth0Id
                    },
                    "tokens": {
                        "access_token": tokens["access_token"],
                        "id_token": tokens.get("id_token"),
                        "token_type": tokens.get("token_type", "Bearer"),
                        "expires_in": tokens.get("expires_in")
                    }
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.exception("Error handling Auth0 callback")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Authentication callback processing failed",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

    # ==================== BACKEND PROCESSING ====================
    
    def _exchange_code_for_tokens(self, authorization_code: str) -> Dict[str, Any]:
        """
        Step 3: Exchange authorization code for access tokens
        """
        token_url = f"https://{self.auth0_domain}/oauth/token"
        
        payload = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": authorization_code,
            "redirect_uri": self.redirect_uri
        }
        
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(token_url, json=payload, headers=headers)
        
        if response.status_code != 200:
            logger.error(f"Token exchange failed: {response.status_code} - {response.text}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Failed to exchange authorization code for tokens",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )
        
        return response.json()

    def _extract_user_info_from_tokens(self, tokens: Dict[str, Any]) -> Dict[str, Any]:
        """
        Step 4: Verify tokens and extract user information
        """
        try:
            # Decode ID token to get user information
            id_token = tokens.get("id_token")
            if not id_token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "message": "ID token not received from Auth0",
                        "status_code": status.HTTP_400_BAD_REQUEST
                    }
                )
            
            # Get unverified claims (we trust Auth0's signature)
            user_claims = jwt.get_unverified_claims(id_token)
            
            # Validate required fields
            required_fields = ["sub", "email"]
            missing_fields = [field for field in required_fields if not user_claims.get(field)]
            
            if missing_fields:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "message": f"Missing required user information: {', '.join(missing_fields)}",
                        "status_code": status.HTTP_400_BAD_REQUEST
                    }
                )
            
            return user_claims
            
        except HTTPException:
            raise
        except Exception as e:
            logger.exception("Error extracting user info from tokens")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Failed to extract user information from tokens",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

    def _get_or_create_member(self, user_info: Dict[str, Any], db: Session) -> CoMember:
        """
        Step 5: Create new user in database or return existing user
        """
        try:
            auth0_id = user_info["sub"]
            email = user_info["email"]
            
            # Check if member already exists
            existing_member = db.query(CoMember).filter_by(auth0Id=auth0_id).first()
            
            if existing_member:
                # Update existing member's Auth0 user data
                self._update_auth0_user_data(user_info, db)
                return existing_member
            
            # Create new member and Auth0 user records
            return self._create_new_member(user_info, db)
            
        except SQLAlchemyError as e:
            db.rollback()
            logger.exception("Database error during member creation/retrieval")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Database error occurred during user processing",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

    def _create_new_member(self, user_info: Dict[str, Any], db: Session) -> CoMember:
        """
        Create new member and Auth0 user records
        """
        try:
            # Create CoAuth0User record
            auth0_user = CoAuth0User(
                userId=user_info["sub"],
                email=user_info["email"],
                name=user_info.get("name", ""),
                familyName=user_info.get("family_name"),
                givenName=user_info.get("given_name"),
                nickName=user_info.get("nickname", ""),
                picture=user_info.get("picture", ""),
                emailVerified=user_info.get("email_verified", False),
                identities=user_info.get("identities", []),
                locale=user_info.get("locale"),
                userMetadata=user_info.get("user_metadata", {}),
                appMetadata=user_info.get("app_metadata", {}),
                lastIp=user_info.get("last_ip", ""),
                createdAt=user_info.get("created_at"),
                updatedAt=user_info.get("updated_at")
            )
            
            # Create CoMember record
            member = CoMember(
                auth0Id=user_info["sub"],
                loginEmail=user_info["email"],
                firstName=user_info.get("given_name"),
                lastName=user_info.get("family_name"),
                loginEmailVerified=user_info.get("email_verified", False)
            )
            
            db.add(auth0_user)
            db.add(member)
            db.commit()
            db.refresh(member)
            db.refresh(auth0_user)
            
            # Set self-referential created/updated fields
            member.createdByMember = member.uuid
            member.updatedByMember = member.uuid
            db.commit()
            db.refresh(member)
            
            logger.info(f"Created new member: {member.uuid} for Auth0 ID: {user_info['sub']}")
            return member
            
        except Exception as e:
            db.rollback()
            logger.exception("Error creating new member")
            raise

    def _update_auth0_user_data(self, user_info: Dict[str, Any], db: Session) -> None:
        """
        Update existing Auth0 user data with latest information
        """
        try:
            auth0_user = db.query(CoAuth0User).filter_by(userId=user_info["sub"]).first()
            if auth0_user:
                # Update with latest information from Auth0
                auth0_user.email = user_info["email"]
                auth0_user.name = user_info.get("name", auth0_user.name)
                auth0_user.familyName = user_info.get("family_name", auth0_user.familyName)
                auth0_user.givenName = user_info.get("given_name", auth0_user.givenName)
                auth0_user.nickName = user_info.get("nickname", auth0_user.nickName)
                auth0_user.picture = user_info.get("picture", auth0_user.picture)
                auth0_user.emailVerified = user_info.get("email_verified", auth0_user.emailVerified)
                auth0_user.updatedAt = user_info.get("updated_at", auth0_user.updatedAt)
                
                db.commit()
                logger.info(f"Updated Auth0 user data for: {user_info['sub']}")
                
        except Exception as e:
            db.rollback()
            logger.exception("Error updating Auth0 user data")
            raise

    # ==================== POST-LOGIN ACTIONS ====================
    
    def enrich_tokens_or_check_access(self, access_token: str, db: Session) -> Dict[str, Any]:
        """
        Step 6: Post-login actions - enrich tokens or check access/log login
        """
        try:
            # Decode access token to get user information
            token_claims = jwt.get_unverified_claims(access_token)
            auth0_id = token_claims.get("sub")
            
            if not auth0_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "message": "Invalid access token: missing user ID",
                        "status_code": status.HTTP_401_UNAUTHORIZED
                    }
                )
            
            # Get member information
            member = db.query(CoMember).filter_by(auth0Id=auth0_id).first()
            if not member:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "message": "Member not found",
                        "status_code": status.HTTP_404_NOT_FOUND
                    }
                )
            
            # Log successful login (you can extend this for audit logging)
            logger.info(f"Successful login for member: {member.uuid}")
            
            return success_response(
                "Token validation successful",
                {
                    "member": {
                        "uuid": str(member.uuid),
                        "email": member.loginEmail,
                        "auth0_id": member.auth0Id,
                        "membership_tier": member.membershipTier,
                        "community_status": member.communityStatus
                    },
                    "token_valid": True
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.exception("Error during token enrichment/access check")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Token validation failed",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

    # ==================== UTILITY METHODS ====================
    
    def _generate_state(self) -> str:
        """Generate a random state parameter for CSRF protection"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
    
    def _validate_state(self, state: str) -> bool:
        """Validate state parameter (implement your own validation logic)"""
        # For now, just check if state exists and has reasonable length
        return state and len(state) >= 16
    
    # ==================== DIRECT LOGIN METHODS ====================
    
    def direct_login(self, email: str, password: str, db: Session) -> Dict[str, Any]:
        """
        Direct username/password login (bypasses OAuth flow)
        """
        try:
            url = f"https://{self.auth0_domain}/oauth/token"
            
            payload = {
                "grant_type": "http://auth0.com/oauth/grant-type/password-realm",
                "username": email,
                "password": password,
                "audience": self.audience,
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "scope": "openid profile email",
                "realm": "Username-Password-Authentication"
            }
            
            response = requests.post(url, json=payload)
            
            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "message": "Invalid credentials",
                        "status_code": status.HTTP_401_UNAUTHORIZED
                    }
                )
            
            tokens = response.json()
            access_token = tokens.get("access_token")
            
            if not access_token:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "message": "Authentication failed",
                        "status_code": status.HTTP_401_UNAUTHORIZED
                    }
                )
            
            # Get user info and return response
            return self.enrich_tokens_or_check_access(access_token, db)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.exception("Error during direct login")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Login failed",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

# Create singleton instance
member_auth_service_v2 = MemberAuthenticationServiceV2()
