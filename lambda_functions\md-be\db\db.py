from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy import Engine
from config import settings
from .connection_manager import initialize_connection_manager
import logging
from typing import Optional, Generator
from models import *

# Configure logging
logger = logging.getLogger(__name__)

# Base class for models
# this is to make pgdb treat a class as table in database
Base = declarative_base()

# Global variables for lazy initialization
_connection_manager = None
_engine = None
_session_local = None

def get_connection_manager():
    # Get or initialize the connection manager
    global _connection_manager
    if _connection_manager is None:
        _connection_manager = initialize_connection_manager(
            # secret_name=settings.AWS_SECRET_NAME, 
            secret_name="stg_member_db_rw_user",
            region="us-east-1"
        )
        logger.info("Database connection manager initialized with AWS Secrets Manager authentication")
    return _connection_manager

def get_engine():
    # Get or create the SQLAlchemy engine
    global _engine
    if _engine is None:
        connection_manager = get_connection_manager()
        _engine = connection_manager.get_engine()
        logger.info("SQLAlchemy engine created")
    return _engine

def get_session_local() -> sessionmaker:
    # Get or create the SQLAlchemy SessionLocal
    global _session_local
    if _session_local is None:
        connection_manager = get_connection_manager()
        _session_local = connection_manager.get_session_local()
        logger.info("SQLAlchemy SessionLocal created")
    return _session_local

# For backward compatibility - these will be initialized on first access
engine = None
SessionLocal = None

def _ensure_initialized():
    # Ensure engine and SessionLocal are initialized
    global engine, SessionLocal
    if engine is None:
        engine = get_engine()
    if SessionLocal is None:
        SessionLocal = get_session_local()

def create_tables():
    # Create all database tables
    try:
        engine = get_engine()
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {str(e)}")
        raise

# Dependency to get a database session
def get_db():
    # FastAPI dependency to get a database session
    db = None
    try:
        _ensure_initialized()
        session_factory = get_session_local()
        db = session_factory()
        yield db
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        if db is not None:
            try:
                db.rollback()
            except:
                pass
        raise
    finally:
        if db is not None:
            try:
                db.close()
            except:
                pass