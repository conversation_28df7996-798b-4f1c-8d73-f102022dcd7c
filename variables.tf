#provider variables that tell terraform which provider to use and where to put it's state file
variable "aws_account_number" {
  description = "Account number for the AWS account"
  type        = string
  default     = "************"
}

variable "aws_region" {
  description = "EC2 region for the VPC"
  type        = string
  default     = "us-east-1"
}

## Label module variables ##
variable "environment" {
  type        = string
  default     = ""
  description = "Stage, e.g. 'prod', 'staging', 'dev'"
}

variable "project" {
  type        = string
  default     = ""
  description = "project name"
}

variable "name" {
  type        = string
  default     = ""
  description = "Solution name, e.g. `app` or `j<PERSON><PERSON>`"
}

variable "classification" {
  type        = string
  default     = ""
  description = "data classification such as private/confidential/public"
}

variable "compliance" {
  type        = string
  default     = ""
  description = "Compliance requirements such as SOC2"
}

variable "delimiter" {
  type        = string
  default     = "-"
  description = "Delimiter to be used between `environment`, `project`, `name` and `attributes`"
}

variable "attributes" {
  type        = list(string)
  default     = []
  description = "Additional attributes (e.g. `1`)"
}

variable "additional_tags" {
  type        = map(string)
  default     = {}
  description = "Additional tags (e.g. `map('BusinessUnit','XYZ')`)"
}

## lambda variables ##
variable "function_name" {
  type        = string
  default     = ""
  description = "specific name for lambda function"
}

variable "description" {
  description = "Description of your Lambda Function (or Layer)"
  type        = string
  default     = ""
}

variable "timeout" {
  description = "The amount of time your Lambda Function has to run in seconds."
  type        = number
  default     = 3
}

variable "memory_size" {
  description = "Amount of memory in MB your Lambda Function can use at runtime. Valid value between 128 MB to 10,240 MB (10 GB), in 64 MB increments."
  type        = number
  default     = 128
} 

variable "environment_variables" {
  description = "A map that defines environment variables for the Lambda Function."
  type        = map(string)
  default     = {}
}

variable "package_type" {
  description = "The Lambda deployment package type. Valid options: Zip or Image"
  type        = string
  default     = "Zip"
}

variable "image_uri" {
  description = "The ECR image URI containing the function's deployment package."
  type        = string
  default     = null
}

## ECR Setup ## 
variable "create_ecr_repo" {
  description = "Controls whether ECR repository for Lambda image should be created"
  type        = bool
  default     = false
} 
  
variable "ecr_repo" {
  description = "Name of ECR repository to use or to create"
  type        = string
  default     = null
} 
  
variable "image_tag_mutability" {
  description = "The tag mutability setting for the repository. Must be one of: `MUTABLE` or `IMMUTABLE`"
  type        = string
  default     = "MUTABLE"
} 
  
variable "scan_on_push" {
  description = "Indicates whether images are scanned after being pushed to the repository"
  type        = bool
  default     = false
} 

variable "ecr_force_delete" {
  description = "If true, will delete the repository even if it contains images."
  default     = true
  type        = bool
}

variable "ecr_repo_tags" {
  description = "A map of tags to assign to ECR repository"
  type        = map(string)
  default     = {}
}

## SG variables ##
variable "lambda_sg_tag_name" {
  description = "tag name for security group"
  type        = string
}

variable "lambda_sg_description" {
  description = "description for security group"
  type        = string
  default     = "Security group for lambda to connect to DB"
}

## Kms key details ##
variable "lambda_kms_alias_name" {
  description = "alias name for kms key"
  type        = string
}

variable "lambda_kms_description" {
  description = "description for kms key"
  type        = string
  default     = "KMS key used for lambda"
}
