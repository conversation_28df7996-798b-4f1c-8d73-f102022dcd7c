from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID

class LogFilters(BaseModel):
    start_timestamp: Optional[datetime] = Field(None, description="Start timestamp for filtering logs")
    end_timestamp: Optional[datetime] = Field(None, description="End timestamp for filtering logs")
    purpose: Optional[str] = Field(None, description="Search text in purpose field")
    action: Optional[str] = Field(None, description="Filter by specific action type")

class LogResponse(BaseModel):
    id: int
    uuid: UUID
    timestamp: datetime
    userUuid: UUID
    username: Optional[str] = Field(None, description="Username of the admin who performed the action")
    admin_uuid: Optional[UUID] = Field(None, description="UUID of the admin who performed the action")
    action: str
    filters_applied: Optional[Dict[str, Any]] = Field(None, description="Filters applied from context JSON")
    selected_fields: Optional[List[str]] = Field(None, description="Selected fields from context JSON")
    purpose: str

    class Config:
        from_attributes = True

class LogListResponse(BaseModel):
    logs: List[LogResponse]
    total_count: int
    page: int
    page_size: int

class LogExportRequest(BaseModel):
    filters: LogFilters
    selected_fields: List[str] = Field(..., description="Fields to include in CSV export")
    notes: Optional[str] = Field(None, description="Purpose of this export action")

class LogExportResponse(BaseModel):
    csv_content: str
    filename: str
    export_timestamp: datetime 