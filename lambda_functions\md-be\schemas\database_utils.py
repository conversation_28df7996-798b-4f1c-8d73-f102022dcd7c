from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


# ========================
# Sequence Reset Schemas
# ========================

class SequenceResetInfo(BaseModel):
    """Information about a single sequence reset operation"""
    sequence_name: str = Field(..., description="Full name of the PostgreSQL sequence")
    table_name: str = Field(..., description="Name of the table associated with the sequence")
    column_name: str = Field(..., description="Name of the column associated with the sequence")
    max_table_id: int = Field(..., description="Maximum ID value found in the table")
    previous_sequence_value: Optional[int] = Field(None, description="Sequence value before reset")
    new_sequence_value: Optional[int] = Field(None, description="Sequence value after reset")
    status: str = Field(..., description="Status of the reset operation (success/failed)")
    error: Optional[str] = Field(None, description="Error message if the operation failed")

    class Config:
        from_attributes = True


class SequenceResetSummary(BaseModel):
    """Summary information about the sequence reset operation"""
    operation: str = Field(..., description="Type of operation performed")
    timestamp: str = Field(..., description="Timestamp when the operation was performed")
    success_rate: str = Field(..., description="Percentage of successful resets")

    class Config:
        from_attributes = True


class SequenceResetResponse(BaseModel):
    """Response schema for sequence reset operations"""
    status_code: int = Field(..., description="HTTP status code")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message about the operation")
    sequences_reset: List[SequenceResetInfo] = Field(..., description="List of sequence reset results")
    total_sequences: int = Field(..., description="Total number of sequences processed")
    successful_resets: int = Field(..., description="Number of sequences successfully reset")
    failed_resets: int = Field(..., description="Number of sequences that failed to reset")
    summary: SequenceResetSummary = Field(..., description="Summary of the operation")

    class Config:
        from_attributes = True


# ========================
# Sequence Status Schemas
# ========================

class SequenceStatusInfo(BaseModel):
    """Information about the current status of a sequence"""
    sequence_name: str = Field(..., description="Full name of the PostgreSQL sequence")
    table_name: str = Field(..., description="Name of the table associated with the sequence")
    column_name: str = Field(..., description="Name of the column associated with the sequence")
    max_table_id: int = Field(..., description="Maximum ID value found in the table")
    current_sequence_value: int = Field(..., description="Current value of the sequence")
    is_out_of_sync: bool = Field(..., description="Whether the sequence is out of sync with the table")
    recommended_next_value: int = Field(..., description="Recommended next value for the sequence")
    status: Optional[str] = Field(None, description="Status if there was an error checking the sequence")
    error: Optional[str] = Field(None, description="Error message if checking failed")

    class Config:
        from_attributes = True


class SequenceStatusSummary(BaseModel):
    """Summary information about the sequence status check"""
    operation: str = Field(..., description="Type of operation performed")
    timestamp: str = Field(..., description="Timestamp when the check was performed")
    needs_reset: bool = Field(..., description="Whether any sequences need to be reset")

    class Config:
        from_attributes = True


class SequenceStatusResponse(BaseModel):
    """Response schema for sequence status check operations"""
    status_code: int = Field(..., description="HTTP status code")
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message about the operation")
    sequences: List[SequenceStatusInfo] = Field(..., description="List of sequence status information")
    total_sequences: int = Field(..., description="Total number of sequences checked")
    out_of_sync_count: int = Field(..., description="Number of sequences that are out of sync")
    summary: SequenceStatusSummary = Field(..., description="Summary of the status check")

    class Config:
        from_attributes = True


# ========================
# Request Schemas
# ========================

class SequenceResetRequest(BaseModel):
    """Request schema for sequence reset operations (currently no parameters needed)"""
    confirm: bool = Field(True, description="Confirmation that the user wants to reset sequences")
    dry_run: Optional[bool] = Field(False, description="If true, only check status without resetting")

    class Config:
        from_attributes = True


# ========================
# Error Response Schemas
# ========================

class DatabaseUtilsErrorResponse(BaseModel):
    """Error response schema for database utilities operations"""
    status_code: int = Field(..., description="HTTP status code")
    success: bool = Field(False, description="Always false for error responses")
    message: str = Field(..., description="Error message")
    detail: Optional[Dict[str, Any]] = Field(None, description="Additional error details")

    class Config:
        from_attributes = True


# ========================
# Generic Response Schemas
# ========================

class DatabaseUtilsSuccessResponse(BaseModel):
    """Generic success response schema for database utilities"""
    status_code: int = Field(..., description="HTTP status code")
    success: bool = Field(True, description="Always true for success responses")
    message: str = Field(..., description="Success message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")

    class Config:
        from_attributes = True
