from pydantic import BaseModel, Field, EmailStr, validator
from typing import Optional, Dict, Any, List
from enum import Enum

class AuthProvider(str, Enum):
    """Supported authentication providers"""
    GOOGLE = "google"
    APPLE = "apple"
    LINKEDIN = "linkedin"
    USERNAME_PASSWORD = "username-password"
    AUTH0 = "auth0"

class AuthInitiationRequest(BaseModel):
    """Request to initiate authentication flow"""
    provider: AuthProvider = Field(..., description="Authentication provider to use")
    redirect_uri: Optional[str] = Field(None, description="Custom redirect URI (optional)")
    
    class Config:
        use_enum_values = True

class AuthInitiationResponse(BaseModel):
    """Response for authentication initiation"""
    auth_url: str = Field(..., description="URL to redirect user for authentication")
    state: str = Field(..., description="State parameter for CSRF protection")
    provider: str = Field(..., description="Provider used for authentication")

class AuthCallbackRequest(BaseModel):
    """Request for handling Auth0 callback"""
    code: str = Field(..., description="Authorization code from Auth0")
    state: str = Field(..., description="State parameter for CSRF validation")
    
    @validator('code')
    def validate_code(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Authorization code cannot be empty')
        return v.strip()
    
    @validator('state')
    def validate_state(cls, v):
        if not v or len(v.strip()) < 16:
            raise ValueError('State parameter must be at least 16 characters')
        return v.strip()

class TokenValidationRequest(BaseModel):
    """Request for token validation and enrichment"""
    access_token: str = Field(..., description="JWT access token to validate")
    
    @validator('access_token')
    def validate_access_token(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Access token cannot be empty')
        return v.strip()

class MemberInfo(BaseModel):
    """Member information returned in authentication responses"""
    uuid: str = Field(..., description="Member UUID")
    email: str = Field(..., description="Member email address")
    auth0_id: str = Field(..., description="Auth0 user ID")
    first_name: Optional[str] = Field(None, description="Member first name")
    last_name: Optional[str] = Field(None, description="Member last name")
    membership_tier: Optional[str] = Field(None, description="Member tier (lite, premium, etc.)")
    community_status: Optional[str] = Field(None, description="Community verification status")
    email_verified: Optional[bool] = Field(None, description="Whether email is verified")

class TokenInfo(BaseModel):
    """Token information returned in authentication responses"""
    access_token: str = Field(..., description="JWT access token")
    id_token: Optional[str] = Field(None, description="JWT ID token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: Optional[int] = Field(None, description="Token expiration time in seconds")

class AuthenticationResponse(BaseModel):
    """Complete authentication response"""
    success: bool = Field(True, description="Whether authentication was successful")
    message: str = Field(..., description="Response message")
    status_code: int = Field(..., description="HTTP status code")
    data: Dict[str, Any] = Field(..., description="Response data")
    member: Optional[MemberInfo] = Field(None, description="Member information")
    tokens: Optional[TokenInfo] = Field(None, description="Authentication tokens")

class DirectLoginRequest(BaseModel):
    """Request for direct username/password login"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password")
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v

class AuthStatusResponse(BaseModel):
    """Authentication service status response"""
    status: str = Field(..., description="Service status")
    auth0_domain: str = Field(..., description="Auth0 domain")
    client_id: str = Field(..., description="Auth0 client ID")
    supported_providers: List[str] = Field(..., description="List of supported authentication providers")
    flow_type: str = Field(..., description="OAuth flow type")
    version: str = Field(..., description="Service version")

class ErrorResponse(BaseModel):
    """Error response schema"""
    success: bool = Field(False, description="Always false for errors")
    message: str = Field(..., description="Error message")
    status_code: int = Field(..., description="HTTP status code")
    error_code: Optional[str] = Field(None, description="Specific error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")

# Legacy compatibility schemas (for backward compatibility)
class LegacyLoginRequest(BaseModel):
    """Legacy login request format"""
    loginEmail: EmailStr = Field(..., description="User login email")
    password: str = Field(..., description="User password")

class LegacyProvider(BaseModel):
    """Legacy provider request format"""
    provider: str = Field(..., description="Authentication provider")
    redirect_uri: str = Field(..., description="Redirect URI")

# Validation schemas for Auth0 user data
class Auth0UserClaims(BaseModel):
    """Auth0 user claims from ID token"""
    sub: str = Field(..., description="Auth0 user ID (subject)")
    email: EmailStr = Field(..., description="User email")
    email_verified: Optional[bool] = Field(None, description="Email verification status")
    name: Optional[str] = Field(None, description="Full name")
    given_name: Optional[str] = Field(None, description="First name")
    family_name: Optional[str] = Field(None, description="Last name")
    nickname: Optional[str] = Field(None, description="Nickname")
    picture: Optional[str] = Field(None, description="Profile picture URL")
    locale: Optional[str] = Field(None, description="User locale")
    updated_at: Optional[str] = Field(None, description="Last update timestamp")
    created_at: Optional[str] = Field(None, description="Creation timestamp")
    identities: Optional[List[Dict[str, Any]]] = Field(None, description="Identity provider information")
    user_metadata: Optional[Dict[str, Any]] = Field(None, description="User metadata")
    app_metadata: Optional[Dict[str, Any]] = Field(None, description="App metadata")

class Auth0TokenResponse(BaseModel):
    """Auth0 token exchange response"""
    access_token: str = Field(..., description="Access token")
    id_token: Optional[str] = Field(None, description="ID token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: Optional[int] = Field(None, description="Expiration time in seconds")
    scope: Optional[str] = Field(None, description="Token scope")
    refresh_token: Optional[str] = Field(None, description="Refresh token")

# Configuration validation schemas
class AuthConfigValidation(BaseModel):
    """Authentication configuration validation"""
    auth0_configured: bool = Field(..., description="Whether Auth0 is properly configured")
    required_settings: List[str] = Field(..., description="List of required configuration settings")
    missing_settings: List[str] = Field(default=[], description="List of missing configuration settings")
    warnings: List[str] = Field(default=[], description="Configuration warnings")
