from attr import validate
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.member_controller import MemberController
from db.db import get_db
from dependencies.jwt_verifier import verify_user
from dependencies.admin_jwt import validate_jwt_token
from schemas.member import CoMemberCreate, CoMemberUpdate, BulkUpsertMembersRequest, BulkDeleteMembersRequest
from typing import List, Optional

router = APIRouter()

memberController = MemberController()

# GET Routes
@router.get("/", dependencies=[Depends(verify_user)])
async def get_all_members(
    db: Session = Depends(get_db), 
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    return memberController.get_all_members(db, page=page, pageSize=pageSize)

@router.get("/uuid/{uuid}", dependencies=[Depends(verify_user)])
async def get_member_by_uuid(uuid: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_uuid(uuid, db)

@router.get("/auth0/{auth0id}", dependencies=[Depends(verify_user)])
async def get_member_by_auth0id(auth0id: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_auth0id(auth0id, db)

@router.get("/loginemail/{email}", dependencies=[Depends(verify_user)])
async def get_member_by_email(email: str, db: Session = Depends(get_db)):
    return memberController.get_member_by_email(email, db)

@router.put("/uuid/{uuid}")
async def update_member_by_uuid(uuid: str, member: CoMemberUpdate, db: Session = Depends(get_db), admin_user_payload=Depends(verify_user)):
    return memberController.update_member_by_uuid(uuid, member, db, admin_user_payload)

@router.delete("/{uuid}")
async def delete_member(uuid: str, db: Session = Depends(get_db), admin_user_payload=Depends(verify_user)):
    return memberController.delete_member(uuid, db, admin_user_payload)

# Bulk Operations (Admin only)
@router.post("/bulk/upsert")
async def bulk_upsert_members(
    request: BulkUpsertMembersRequest,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    return memberController.bulk_upsert_members(request, db, admin_user_payload)

@router.get("/bulk/with-organizations", dependencies=[Depends(validate_jwt_token)])
async def get_members_with_organizations(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    # Optional filter parameters
    firstName: Optional[str] = Query(None, description="Filter by member first name (partial match)"),
    lastName: Optional[str] = Query(None, description="Filter by member last name (partial match)"),
    email: Optional[str] = Query(None, description="Filter by member email (partial match on login or business email)"),
    membershipTier: Optional[str] = Query(None, description="Filter by membership tier (partial match)"),
    communityStatus: Optional[str] = Query(None, description="Filter by community status (partial match)"),
    organizationName: Optional[str] = Query(None, description="Filter by organization name (partial match)"),
    organizationCity: Optional[str] = Query(None, description="Filter by organization city (partial match)"),
    organizationState: Optional[str] = Query(None, description="Filter by organization state (partial match)"),
    organizationZip: Optional[str] = Query(None, description="Filter by organization ZIP code (partial match)"),
    companySize: Optional[str] = Query(None, description="Filter by company size (partial match)"),
    dateCreatedFrom: Optional[str] = Query(None, description="Filter members created from this date (ISO format: YYYY-MM-DDTHH:MM:SSZ)"),
    dateCreatedTo: Optional[str] = Query(None, description="Filter members created up to this date (ISO format: YYYY-MM-DDTHH:MM:SSZ)")
):
    """
    Get all members with their associated organizations, with optional filtering and pagination.
    
    This endpoint supports filtering by various member and organization fields, including date range filtering.
    All filters are optional and use case-insensitive partial matching (except date filters which use exact comparison).
    
    Date filtering supports:
    - dateCreatedFrom: Members created from this date onwards
    - dateCreatedTo: Members created up to this date
    - Both parameters can be used together for range filtering
    
    Performance optimized to avoid N+1 query issues using proper database joins.
    """
    return memberController.get_members_with_organizations(
        db, page, pageSize, firstName, lastName, email, membershipTier,
        communityStatus, organizationName, organizationCity, organizationState,
        organizationZip, companySize, dateCreatedFrom, dateCreatedTo
    )

@router.delete("/bulk/delete")
async def bulk_delete_members(
    request: BulkDeleteMembersRequest,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    return memberController.bulk_delete_members(request, db, admin_user_payload)