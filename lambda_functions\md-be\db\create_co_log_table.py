#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the co_log table for universal logging functionality.
This script uses the existing database connection configuration.
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.db import get_engine, create_tables
from models.log import CoLog
from sqlalchemy import text
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_co_log_table():
    """
    Create the co_log table using SQLAlchemy
    """
    try:
        logger.info("🔧 Creating co_log table...")
        
        # Get the database engine
        engine = get_engine()
        
        # Create the table using SQLAlchemy
        CoLog.__table__.create(engine, checkfirst=True)
        
        logger.info("✅ co_log table created successfully!")
        
        # Verify the table exists
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM co_log"))
            count = result.scalar()
            logger.info(f"✅ Table verification: co_log table exists with {count} records")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating co_log table: {str(e)}")
        return False

def create_indexes():
    """
    Create indexes for better performance
    """
    try:
        logger.info("🔧 Creating indexes for co_log table...")
        
        engine = get_engine()
        
        with engine.connect() as conn:
            # Create indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_co_log_user_uuid ON co_log(\"userUuid\")",
                "CREATE INDEX IF NOT EXISTS idx_co_log_timestamp ON co_log(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_co_log_action ON co_log(action)"
            ]
            
            for index_sql in indexes:
                conn.execute(text(index_sql))
                logger.info(f"✅ Created index: {index_sql}")
            
            conn.commit()
            
        logger.info("✅ All indexes created successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating indexes: {str(e)}")
        return False

def verify_table_structure():
    """
    Verify the table structure is correct
    """
    try:
        logger.info("🔍 Verifying table structure...")
        
        engine = get_engine()
        
        with engine.connect() as conn:
            # Check table structure
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'co_log' 
                ORDER BY ordinal_position
            """))
            
            columns = result.fetchall()
            
            logger.info("📋 co_log table structure:")
            for column in columns:
                logger.info(f"  - {column[0]}: {column[1]} ({'NULL' if column[2] == 'YES' else 'NOT NULL'})")
            
            # Check if all required columns exist
            required_columns = ['id', 'uuid', 'timestamp', 'userUuid', 'action', 'context', 'purpose']
            existing_columns = [col[0] for col in columns]
            
            missing_columns = set(required_columns) - set(existing_columns)
            if missing_columns:
                logger.error(f"❌ Missing columns: {missing_columns}")
                return False
            else:
                logger.info("✅ All required columns exist!")
                return True
                
    except Exception as e:
        logger.error(f"❌ Error verifying table structure: {str(e)}")
        return False

def test_log_insertion():
    """
    Test inserting a log entry to verify everything works
    """
    try:
        logger.info("🧪 Testing log insertion...")
        
        from sqlalchemy.orm import Session
        from db.db import get_session_local
        import uuid
        
        # Create a test log entry
        test_log = CoLog(
            userUuid=uuid.uuid4(),
            action="test_insertion",
            context={"test": True, "message": "Testing co_log table creation"},
            purpose="Testing table creation script"
        )
        
        # Get session and insert
        SessionLocal = get_session_local()
        with SessionLocal() as db:
            db.add(test_log)
            db.commit()
            db.refresh(test_log)
            
            logger.info(f"✅ Test log entry created with ID: {test_log.id}")
            
            # Clean up test entry
            db.delete(test_log)
            db.commit()
            logger.info("✅ Test log entry cleaned up")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing log insertion: {str(e)}")
        return False

def main():
    """
    Main function to create the co_log table
    """
    logger.info("🚀 Starting co_log table creation...")
    
    # Step 1: Create the table
    if not create_co_log_table():
        logger.error("❌ Failed to create co_log table")
        return False
    
    # Step 2: Create indexes
    if not create_indexes():
        logger.error("❌ Failed to create indexes")
        return False
    
    # Step 3: Verify table structure
    if not verify_table_structure():
        logger.error("❌ Table structure verification failed")
        return False
    
    # Step 4: Test insertion
    if not test_log_insertion():
        logger.error("❌ Log insertion test failed")
        return False
    
    logger.info("🎉 co_log table creation completed successfully!")
    logger.info("📝 The export functionality should now work with audit logging.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 