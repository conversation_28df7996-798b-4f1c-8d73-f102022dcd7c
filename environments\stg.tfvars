### label details ###
name           = "member-be-api"
environment    = "stg"
project        = "uscc-co"
classification = "confidential"
compliance     = "SOC2"

function_name = "us-east1-stg-member-be-api"
package_type  = "Image"
description   = "stg member database be api"
timeout       = 300
memory_size   = 128

ecr_repo      = "member-be-api"

## Security Group Variables ###
lambda_sg_tag_name = "SG-STG-MEMBER-BE-API"

## Kms variables ##
lambda_kms_alias_name = "KMS-STG-MEMBER-BE-API"
