from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from services.member_authentication_service_v2 import member_auth_service_v2
from schemas.member import LoginRequest
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class MemberAuthenticationControllerV2:
    """
    Enhanced Member Authentication Controller implementing the complete OAuth2/Auth0 flow
    Based on the provided authentication flow diagram
    """
    
    def __init__(self):
        self.auth_service = member_auth_service_v2

    # ==================== FRONTEND INTERACTION ====================
    
    def initiate_social_login(self, provider: str, redirect_uri: Optional[str] = None) -> Dict[str, Any]:
        """
        Step 1: User starts authentication - Generate Auth0 login URL
        
        Args:
            provider: Authentication provider (google, apple, linkedin, username-password)
            redirect_uri: Optional custom redirect URI
            
        Returns:
            Dict containing the Auth0 authentication URL and state parameter
        """
        try:
            return self.auth_service.initiate_authentication(provider, redirect_uri)
        except Exception as e:
            logger.exception("Error initiating social login")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to initiate authentication",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

    # ==================== AUTH0 PROCESSING ====================
    
    def handle_auth_callback(self, authorization_code: str, state: str, db: Session) -> Dict[str, Any]:
        """
        Step 2-5: Handle Auth0 callback and complete authentication flow
        
        This method handles:
        - Auth0 Universal Login completion
        - User registration or login processing  
        - Authorization code exchange for tokens
        - User creation in database
        - Token verification and user info extraction
        
        Args:
            authorization_code: Authorization code from Auth0
            state: State parameter for CSRF protection
            db: Database session
            
        Returns:
            Dict containing member info and authentication tokens
        """
        try:
            return self.auth_service.handle_auth0_callback(authorization_code, state, db)
        except Exception as e:
            logger.exception("Error handling auth callback")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Authentication callback processing failed",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

    # ==================== POST-LOGIN ACTIONS ====================
    
    def validate_and_enrich_token(self, access_token: str, db: Session) -> Dict[str, Any]:
        """
        Step 6: Post-login actions - Validate token and enrich with user data
        
        This method handles:
        - Access token validation
        - User information retrieval
        - Login logging/audit
        - Token enrichment with user context
        
        Args:
            access_token: JWT access token from Auth0
            db: Database session
            
        Returns:
            Dict containing validated user info and enriched token data
        """
        try:
            return self.auth_service.enrich_tokens_or_check_access(access_token, db)
        except Exception as e:
            logger.exception("Error validating and enriching token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "message": "Token validation failed",
                    "status_code": status.HTTP_401_UNAUTHORIZED
                }
            )

    # ==================== DIRECT LOGIN METHODS ====================
    
    def direct_username_password_login(self, login_request: LoginRequest, db: Session) -> Dict[str, Any]:
        """
        Direct username/password login (bypasses OAuth flow)
        
        This is an alternative to the OAuth flow for direct credential authentication.
        
        Args:
            login_request: Login credentials (email and password)
            db: Database session
            
        Returns:
            Dict containing member info and authentication tokens
        """
        try:
            return self.auth_service.direct_login(
                login_request.loginEmail, 
                login_request.password, 
                db
            )
        except Exception as e:
            logger.exception("Error during direct login")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "message": "Login failed",
                    "status_code": status.HTTP_401_UNAUTHORIZED
                }
            )

    # ==================== UTILITY METHODS ====================
    
    def get_auth_status(self) -> Dict[str, Any]:
        """
        Get authentication service status and configuration
        
        Returns:
            Dict containing service status and configuration info
        """
        try:
            return {
                "status": "active",
                "auth0_domain": self.auth_service.auth0_domain,
                "client_id": self.auth_service.client_id,
                "supported_providers": [
                    "google",
                    "apple", 
                    "linkedin",
                    "username-password"
                ],
                "flow_type": "authorization_code_with_pkce",
                "version": "2.0"
            }
        except Exception as e:
            logger.exception("Error getting auth status")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "message": "Failed to get authentication status",
                    "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
                }
            )

# Create singleton instance
member_auth_controller_v2 = MemberAuthenticationControllerV2()
