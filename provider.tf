#terraform backend for storing state file
terraform {
  backend "s3" {
    bucket       = "uscc-co-************-ops-terraform-useast1"
    region       = "us-east-1"
    encrypt      = "true"
    use_lockfile = "true"
  }
}

#setup aws provider
provider "aws" {
  region              = var.aws_region
  allowed_account_ids = [var.aws_account_number]
}

#Vpc remote state
data "terraform_remote_state" "vpc" {
  backend = "s3"
  config = {
    bucket = "uscc-co-************-ops-terraform-useast1"
    key    = "aws-************-set-mgmt-useast1"
    region = var.aws_region
  }
}
