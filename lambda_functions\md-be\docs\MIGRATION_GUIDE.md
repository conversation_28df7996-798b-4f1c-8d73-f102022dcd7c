# Migration Guide: Authentication Service V1 to V2

## Overview

This guide helps you migrate from the existing member authentication service to the enhanced V2 implementation. The new service provides improved security, better error handling, and a more robust OAuth2 flow while maintaining backward compatibility.

## Key Differences

### V1 vs V2 Comparison

| Feature | V1 | V2 |
|---------|----|----|
| **Flow Type** | Mixed OAuth/Direct | Complete OAuth2 with PKCE |
| **CSRF Protection** | None | State parameter validation |
| **Error Handling** | Basic | Comprehensive with detailed responses |
| **Token Validation** | Basic JWT decode | Full JWT validation with JWKS |
| **User Management** | Simple create/update | Enhanced with metadata sync |
| **API Structure** | Single endpoints | Structured flow-based endpoints |
| **Documentation** | Minimal | Comprehensive with examples |
| **Testing** | Limited | Full test coverage |

## Migration Strategy

### Phase 1: Parallel Deployment (Recommended)
1. Deploy V2 service alongside V1
2. Test V2 endpoints thoroughly
3. Gradually migrate frontend components
4. Monitor both services during transition
5. Deprecate V1 after full migration

### Phase 2: Direct Migration (Alternative)
1. Update all frontend code simultaneously
2. Deploy V2 service
3. Remove V1 endpoints

## API Endpoint Mapping

### Authentication Initiation
**V1 Endpoint:**
```
POST /api/members/social-login
{
  "provider": "google",
  "redirect_uri": "https://app.com/callback"
}
```

**V2 Endpoint:**
```
POST /api/members/v2/auth/initiate
{
  "provider": "google",
  "redirect_uri": "https://app.com/callback"
}
```

### Callback Handling
**V1 Endpoint:**
```
GET /api/members/callback?code=auth_code
```

**V2 Endpoint:**
```
POST /api/members/v2/auth/callback
{
  "code": "auth_code",
  "state": "csrf_state"
}
```

### Direct Login
**V1 Endpoint:**
```
POST /api/members/login
{
  "loginEmail": "<EMAIL>",
  "password": "password123"
}
```

**V2 Endpoint:**
```
POST /api/members/v2/auth/direct-login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## Frontend Migration

### React/JavaScript Example

#### V1 Implementation
```javascript
// V1 - Social Login
const initiateLogin = async (provider) => {
  const response = await fetch('/api/members/social-login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      provider: provider,
      redirect_uri: window.location.origin + '/callback'
    })
  });
  
  const data = await response.json();
  window.location.href = data.url;
};

// V1 - Handle Callback
const handleCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  
  const response = await fetch(`/api/members/callback?code=${code}`);
  const data = await response.json();
  
  localStorage.setItem('token', data.data.token);
};
```

#### V2 Implementation
```javascript
// V2 - Social Login
const initiateLogin = async (provider) => {
  const response = await fetch('/api/members/v2/auth/initiate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      provider: provider,
      redirect_uri: window.location.origin + '/callback'
    })
  });
  
  const data = await response.json();
  
  // Store state for CSRF protection
  sessionStorage.setItem('auth_state', data.data.state);
  
  window.location.href = data.data.auth_url;
};

// V2 - Handle Callback
const handleCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const state = urlParams.get('state');
  
  // Verify state parameter
  const storedState = sessionStorage.getItem('auth_state');
  if (state !== storedState) {
    throw new Error('Invalid state parameter');
  }
  
  const response = await fetch('/api/members/v2/auth/callback', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ code, state })
  });
  
  const data = await response.json();
  
  if (data.success) {
    localStorage.setItem('access_token', data.data.tokens.access_token);
    localStorage.setItem('member_info', JSON.stringify(data.data.member));
  }
  
  // Clean up
  sessionStorage.removeItem('auth_state');
};

// V2 - Token Validation
const validateToken = async (token) => {
  const response = await fetch('/api/members/v2/auth/validate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ access_token: token })
  });
  
  return response.json();
};
```

## Database Migration

### No Schema Changes Required
The V2 service uses the same database schema as V1:
- `co_members` table remains unchanged
- `co_auth0_users` table remains unchanged
- All existing data is compatible

### Data Validation (Optional)
Run this script to validate existing data:

```sql
-- Check for members without Auth0 users
SELECT m.uuid, m.auth0Id, m.loginEmail 
FROM co_members m 
LEFT JOIN co_auth0_users a ON m.auth0Id = a.userId 
WHERE a.userId IS NULL;

-- Check for Auth0 users without members
SELECT a.userId, a.email 
FROM co_auth0_users a 
LEFT JOIN co_members m ON a.userId = m.auth0Id 
WHERE m.auth0Id IS NULL;

-- Validate email consistency
SELECT m.uuid, m.loginEmail, a.email 
FROM co_members m 
JOIN co_auth0_users a ON m.auth0Id = a.userId 
WHERE m.loginEmail != a.email;
```

## Configuration Updates

### Environment Variables
No new environment variables required. V2 uses the same Auth0 configuration:

```env
DOMAIN=dev-gufgtjtn8u27pszl.us.auth0.com
AUDIENCE=https://dev-gufgtjtn8u27pszl.us.auth0.com/api/v2/
CLIENT_ID=X7shlhfc6RXSJBF8tCLxAYRvippPpe1D
CLIENT_SECRET=****************************************************************
REDIRECT_URI=https://stage-console.co-nomi.app/login
SCOPE=openid profile email
```

### Auth0 Dashboard Updates
Follow the [Auth0 Dashboard Setup Guide](./AUTH0_DASHBOARD_SETUP.md) for any additional configuration.

## Testing Migration

### 1. Unit Tests
```bash
# Run V2 tests
python -m pytest tests/test_member_authentication_v2.py -v

# Run existing V1 tests to ensure no regression
python -m pytest tests/test_member_authentication.py -v
```

### 2. Integration Tests
```bash
# Test V2 endpoints
curl -X POST http://localhost:8000/api/members/v2/auth/initiate \
  -H "Content-Type: application/json" \
  -d '{"provider": "google"}'

# Test legacy compatibility
curl -X POST http://localhost:8000/api/members/v2/auth/legacy/social-login \
  -H "Content-Type: application/json" \
  -d '{"provider": "google", "redirect_uri": "http://localhost:3000/callback"}'
```

### 3. Load Testing
```bash
# Use your preferred load testing tool
# Example with Apache Bench
ab -n 1000 -c 10 -H "Content-Type: application/json" \
  -p auth_request.json \
  http://localhost:8000/api/members/v2/auth/initiate
```

## Rollback Plan

### If Issues Occur
1. **Immediate Rollback**: Switch frontend to use V1 endpoints
2. **Database Rollback**: No database changes needed
3. **Configuration Rollback**: Revert Auth0 settings if modified

### Rollback Script Example
```javascript
// Emergency rollback - switch to V1 endpoints
const EMERGENCY_ROLLBACK = true;

const getAuthEndpoint = (endpoint) => {
  if (EMERGENCY_ROLLBACK) {
    return `/api/members/${endpoint}`;  // V1
  }
  return `/api/members/v2/auth/${endpoint}`;  // V2
};
```

## Monitoring and Alerts

### Key Metrics to Monitor
- Authentication success/failure rates
- Response times for auth endpoints
- Database connection health
- Auth0 API response times
- Error rates by endpoint

### Recommended Alerts
```yaml
# Example alert configuration
alerts:
  - name: "High Auth Failure Rate"
    condition: "auth_failure_rate > 5%"
    duration: "5m"
    
  - name: "Slow Auth Response"
    condition: "auth_response_time > 2s"
    duration: "2m"
    
  - name: "Database Connection Issues"
    condition: "db_connection_errors > 0"
    duration: "1m"
```

## Support and Troubleshooting

### Common Migration Issues

1. **CORS Errors**
   - Update CORS settings to include V2 endpoints
   - Verify preflight requests are handled

2. **State Parameter Validation Failures**
   - Ensure frontend stores and validates state parameter
   - Check session storage implementation

3. **Token Format Differences**
   - V2 returns structured token object
   - Update frontend token handling logic

4. **Callback URL Mismatches**
   - Verify Auth0 callback URLs include V2 endpoints
   - Update redirect URI configuration

### Getting Help
- Check the [Authentication Flow Documentation](./AUTHENTICATION_FLOW_V2.md)
- Review [Auth0 Dashboard Setup](./AUTH0_DASHBOARD_SETUP.md)
- Run the test suite for debugging
- Check application logs for detailed error messages

## Timeline Recommendations

### Week 1: Preparation
- Deploy V2 service in staging
- Update Auth0 configuration
- Implement frontend changes
- Run comprehensive tests

### Week 2: Gradual Migration
- Deploy V2 to production (parallel with V1)
- Migrate 10% of traffic to V2
- Monitor metrics and error rates
- Fix any issues discovered

### Week 3: Full Migration
- Migrate remaining traffic to V2
- Monitor system stability
- Prepare rollback if needed

### Week 4: Cleanup
- Remove V1 endpoints (optional)
- Update documentation
- Archive old code
- Celebrate successful migration! 🎉
