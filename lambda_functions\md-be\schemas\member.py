from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional, List
from uuid import UUID
from pydantic import validate_email
from datetime import datetime
from models.member_verification import VerificationStatus

# Schema for creating a CoMember
class CoMemberCreate(BaseModel):
    password: str = Field(..., description="Password for the member")
    customerIoId: Optional[str] = Field(None)
    openWaterId: Optional[int] = Field(None)
    firstName: Optional[str] = Field(None)
    lastName: Optional[str] = Field(None)
    loginEmail: EmailStr = Field(..., description="Login email of the member")
    loginEmailVerified: bool = Field(True)
    identityType: Optional[str] = Field(None)
    personalBusinessEmail: Optional[str] = Field(None)
    phone: Optional[str] = Field(None)
    professionalTitle: Optional[str] = Field(None)
    membershipTier: str = Field("lite", description="Membership tier of the member")
    communityStatus: str = Field("unverified", description="Status of the member in the community")
    hasSeenFirstLoginMessage: bool = Field(False, description="Whether the member has seen the first login message")

    class Config:
        from_attributes = True
        
class CoMemberCreateEnhanced(BaseModel):
    password: str = Field(..., description="Password for the member")
    loginEmail: EmailStr = Field(..., description="Login email of the member")

    class Config:
        from_attributes = True

# Schema for updating a CoMember (password is optional)
class CoMemberUpdate(BaseModel):
    password: Optional[str] = Field(None, description="Password for the member (optional for updates)")
    customerIoId: Optional[str] = Field(None)
    openWaterId: Optional[int] = Field(None)
    firstName: Optional[str] = Field(None)
    lastName: Optional[str] = Field(None)
    loginEmail: Optional[EmailStr] = Field(None, description="Login email of the member")
    loginEmailVerified: Optional[bool] = Field(None)
    identityType: Optional[str] = Field(None)
    personalBusinessEmail: Optional[str] = Field(None)
    phone: Optional[str] = Field(None)
    professionalTitle: Optional[str] = Field(None)
    membershipTier: Optional[str] = Field(None, description="Membership tier of the member")
    communityStatus: Optional[str] = Field(None, description="Status of the member in the community")
    hasSeenFirstLoginMessage: Optional[bool] = Field(None, description="Whether the member has seen the first login message")
    
    verification_status: VerificationStatus = Field(
        VerificationStatus.pending,  # use Enum value as default
        description="Verification status of the member"
    )

    @field_validator("personalBusinessEmail")
    def validate_personal_business_email(cls, v: Optional[str]):
        if v == "" or v is None:
            return v  # Allow empty string or null
        try:
            validate_email(v)  # Will raise if not a valid email
        except ValueError:
            raise ValueError("Invalid email format for personalBusinessEmail")
        return v
    
    class Config:
        from_attributes = True


# Schema for fetching a CoMember
class CoMemberResponse(BaseModel):
    uuid: UUID = Field(..., description="UUID of the member")
    openWaterId: Optional[int] = Field(None, description="OpenWater ID of the member")
    firstName: Optional[str] = Field(None, description="First name of the member")
    lastName: Optional[str] = Field(None, description="Last name of the member")
    loginEmail: Optional[EmailStr] = Field(None, description="Login email of the member")
    loginEmailVerified: bool = Field(False, description="Whether the login email is verified")
    identityType: Optional[str] = Field(None, description="Type of identity")
    customerIoId: Optional[str] = Field(None, description="Customer.io ID of the member")
    personalBusinessEmail: Optional[str] = Field(None, description="Personal or business email")
    phone: Optional[str] = Field(None, description="Phone number of the member")
    professionalTitle: Optional[str] = Field(None, description="Professional title of the member")
    membershipTier: str = Field(..., description="Membership tier of the member")
    communityStatus: str = Field("unverified", description="Status of the member in the community")
    hasSeenFirstLoginMessage: bool = Field(False, description="Whether the member has seen the first login message")
    dateCreated: datetime = Field(..., description="Date created")
    dateUpdated: datetime = Field(..., description="Date updated")

    @field_validator('loginEmail', 'personalBusinessEmail', mode='before')
    @classmethod
    def validate_email_fields(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

    class Config:
        from_attributes = True

class LoginRequest(BaseModel):
    loginEmail: str = Field("<EMAIL>", description="Login email of the member")
    password: str = Field("Admin@123", description="Password for the member")
    
class Provider(BaseModel):
    provider: str
    redirect_uri: str

# ========================
# Bulk Operations Schemas
# ========================

# Schema for bulk upsert members
class CoMemberBulkUpsert(BaseModel):
    password: Optional[str] = Field(None, description="Password for the member (required for new members)")
    customerIoId: Optional[str] = Field(None)
    openWaterId: Optional[int] = Field(None)
    firstName: Optional[str] = Field(None)
    lastName: Optional[str] = Field(None)
    loginEmail: EmailStr = Field(..., description="Login email of the member (required, used as unique identifier)")
    loginEmailVerified: Optional[bool] = Field(None)
    identityType: Optional[str] = Field(None)
    personalBusinessEmail: Optional[str] = Field(None)
    phone: Optional[str] = Field(None)
    professionalTitle: Optional[str] = Field(None)
    membershipTier: Optional[str] = Field(None, description="Membership tier of the member")
    communityStatus: Optional[str] = Field(None, description="Status of the member in the community")
    hasSeenFirstLoginMessage: Optional[bool] = Field(None, description="Whether the member has seen the first login message")

    @field_validator("personalBusinessEmail")
    def validate_personal_business_email(cls, v: Optional[str]):
        if v == "" or v is None:
            return v  # Allow empty string or null
        try:
            validate_email(v)  # Will raise if not a valid email
        except ValueError:
            raise ValueError("Invalid email format for personalBusinessEmail")
        return v
    
    class Config:
        from_attributes = True

# Schema for bulk upsert request
class BulkUpsertMembersRequest(BaseModel):
    members: List[CoMemberBulkUpsert] = Field(..., description="List of members to upsert (max 500)")

    @field_validator('members')
    @classmethod
    def validate_members_count(cls, v):
        if len(v) > 500:
            raise ValueError('Maximum 500 members allowed per bulk operation')
        if len(v) == 0:
            raise ValueError('At least one member is required')
        return v

# Schema for individual member operation result
class MemberOperationResult(BaseModel):
    loginEmail: str = Field(..., description="Login email of the member")
    success: bool = Field(..., description="Whether the operation was successful")
    action: str = Field(..., description="Action performed: 'created', 'updated', 'failed'")
    message: str = Field(..., description="Success or error message")
    memberUuid: Optional[UUID] = Field(None, description="UUID of the member (if successful)")

# Schema for bulk upsert response
class BulkUpsertMembersResponse(BaseModel):
    totalProcessed: int = Field(..., description="Total number of members processed")
    successful: int = Field(..., description="Number of successful operations")
    failed: int = Field(..., description="Number of failed operations")
    results: List[MemberOperationResult] = Field(..., description="Detailed results for each member")

# Schema for organization info in member response
class OrganizationInfo(BaseModel):
    name: Optional[str] = Field(None, description="Organization name")
    phone: Optional[str] = Field(None, description="Organization phone")
    email: Optional[str] = Field(None, description="Organization email")
    companySize: Optional[str] = Field(None, description="Organization company size")
    city: Optional[str] = Field(None, description="Organization city")
    state: Optional[str] = Field(None, description="Organization state")
    zip: Optional[str] = Field(None, description="Organization zip")
    industry: Optional[str] = Field(None, description="Organization industry")
    dateCreated: datetime = Field(..., description="Creation timestamp")
    class Config:
        from_attributes = True

# Schema for member with organizations
class CoMemberWithOrganizations(BaseModel):
    uuid: UUID = Field(..., description="UUID of the member")
    customerIoId: Optional[str] = Field(None, description="Customer.io identifier")
    openWaterId: Optional[int] = Field(None, description="OpenWater identifier")
    firstName: Optional[str] = Field(None, description="First name of the member")
    lastName: Optional[str] = Field(None, description="Last name of the member")
    loginEmail: Optional[EmailStr] = Field(None, description="Login email of the member")
    loginEmailVerified: bool = Field(False, description="Whether the login email is verified")
    identityType: Optional[str] = Field(None, description="Type of identity")
    personalBusinessEmail: Optional[str] = Field(None, description="Personal or business email")
    phone: Optional[str] = Field(None, description="Phone number of the member")
    professionalTitle: Optional[str] = Field(None, description="Professional title of the member")
    membershipTier: str = Field(..., description="Membership tier of the member")
    communityStatus: str = Field("unverified", description="Status of the member in the community")
    hasSeenFirstLoginMessage: bool = Field(False, description="Whether the member has seen the first login message")
    organizations: List[OrganizationInfo] = Field(default=[], description="List of associated organizations")

    @field_validator('loginEmail', 'personalBusinessEmail', mode='before')
    @classmethod
    def validate_email_fields(cls, v):
        """Convert empty strings to None for email fields"""
        if v == '' or v is None:
            return None
        return v

    class Config:
        from_attributes = True

# Schema for bulk delete request
class BulkDeleteMembersRequest(BaseModel):
    memberUuids: List[UUID] = Field(..., description="List of member UUIDs to delete (max 500)")

    @field_validator('memberUuids')
    @classmethod
    def validate_uuids_count(cls, v):
        if len(v) > 500:
            raise ValueError('Maximum 500 member UUIDs allowed per bulk operation')
        if len(v) == 0:
            raise ValueError('At least one member UUID is required')
        return v

# Schema for individual delete result
class MemberDeleteResult(BaseModel):
    memberUuid: UUID = Field(..., description="UUID of the member")
    success: bool = Field(..., description="Whether the deletion was successful")
    message: str = Field(..., description="Success or error message")

# Schema for bulk delete response
class BulkDeleteMembersResponse(BaseModel):
    totalProcessed: int = Field(..., description="Total number of members processed")
    successful: int = Field(..., description="Number of successful deletions")
    failed: int = Field(..., description="Number of failed deletions")
    results: List[MemberDeleteResult] = Field(..., description="Detailed results for each member")