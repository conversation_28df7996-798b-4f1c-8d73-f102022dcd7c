from fastapi import APIRouter, Depends, status, Request, Query
from sqlalchemy.orm import Session
from controller.member_authentication_controller import MemberAuthenticationController
from controller.member_auth_triggers_controller import MemberAuthTriggersController
from db.db import get_db
from schemas.member import CoMemberCreate, LoginRequest, Provider, CoMemberCreateEnhanced
from pydantic import BaseModel

router = APIRouter()

member_auth_controller = MemberAuthenticationController()
member_auth_triggers_controller = MemberAuthTriggersController()

# Schema for trigger endpoints
class PostRegistrationRequest(BaseModel):
    email: str
    user_id: str

class AuthCodeRequest(BaseModel):
    code: str

# authentication Routes
@router.post("/register", status_code=status.HTTP_201_CREATED)
async def create(member: CoMemberCreateEnhanced, db: Session = Depends(get_db)):
    user = {}
    return member_auth_controller.create_member(member, user, db)

@router.post("/login", status_code=status.HTTP_200_OK)
async def login(payload: LoginRequest, db: Session = Depends(get_db)):
    return member_auth_controller.login(payload, db)

# auth0 social login routes
@router.post("/social-login", status_code=status.HTTP_200_OK)
async def login_redirect(provider: Provider):
    return member_auth_controller.login_redirect(provider.provider, provider.redirect_uri)

@router.get("/callback", status_code=status.HTTP_200_OK)
async def callback(
        code: str = Query(..., description="Authorization code from provider"),
        db: Session = Depends(get_db)
    ):
    # code = request.query_params.get("code")
    if code is None:
        return {"error": "Missing 'code' parameter in callback."}

    return member_auth_controller.callback(code, db)

# Auth0 Trigger Endpoints
@router.post("/post-registration", status_code=status.HTTP_201_CREATED)
async def post_registration_trigger(
    request: PostRegistrationRequest,
    db: Session = Depends(get_db)
):
    """
    Post-registration trigger endpoint
    Called from Auth0 post-registration action with user email and ID
    Creates member in database using provided user details
    """
    return member_auth_triggers_controller.handle_post_registration(request.email, request.user_id, db)

@router.post("/post-login", status_code=status.HTTP_200_OK)
async def post_login_trigger(
    request: AuthCodeRequest,
    db: Session = Depends(get_db)
):
    """
    Post-login trigger endpoint
    Called after user completes login on Auth0 Universal Login
    Exchanges authorization code for tokens, verifies with Auth0 JWKS and returns member info with access token
    """
    return member_auth_triggers_controller.handle_post_login(request.code, db)
