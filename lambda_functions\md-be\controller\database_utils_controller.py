import logging
from typing import Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException
from services.database_utils_api import reset_all_sequences, get_sequence_status
from schemas.database_utils import SequenceResetRequest

logger = logging.getLogger(__name__)


class DatabaseUtilsController:
    """Controller for database utility operations"""
    
    def reset_sequences(self, request: SequenceResetRequest, admin_user_payload: dict, db: Session) -> Dict[str, Any]:
        """
        Reset all PostgreSQL sequences to fix primary key conflicts.
        
        This endpoint is restricted to admin users only and performs the following:
        1. Discovers all auto-incrementing sequences in the database
        2. Finds the maximum ID value for each associated table
        3. Resets each sequence to start from max_id + 1
        
        Args:
            request: Request containing confirmation and optional dry_run flag
            admin_user_payload: JWT payload containing admin user information
            db: SQLAlchemy database session
            
        Returns:
            Dictionary containing the results of the sequence reset operation
            
        Raises:
            HTTPException: If user is not authorized or if there's an error during reset
        """
        try:
            # Log the operation attempt
            admin_user_id = admin_user_payload.get('sub', 'unknown')
            admin_username = admin_user_payload.get('preferred_username', 'unknown')
            
            logger.info(f"Sequence reset requested by admin user: {admin_username} (ID: {admin_user_id})")
            
            # Validate the request
            if not request.confirm:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "message": "Sequence reset requires explicit confirmation. Set 'confirm' to true.",
                        "status_code": 400
                    }
                )
            
            # If dry_run is requested, return status instead of resetting
            if request.dry_run:
                logger.info(f"Dry run sequence status check requested by admin: {admin_username}")
                return get_sequence_status(db)
            
            # Perform the actual sequence reset
            logger.info(f"Performing sequence reset operation for admin: {admin_username}")
            result = reset_all_sequences(db)
            
            # Log the completion
            if result.get('success', False):
                successful_resets = result.get('successful_resets', 0)
                failed_resets = result.get('failed_resets', 0)
                logger.info(
                    f"Sequence reset completed by admin {admin_username}. "
                    f"Success: {successful_resets}, Failed: {failed_resets}"
                )
            else:
                logger.error(f"Sequence reset failed for admin {admin_username}")
            
            return result
            
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error during sequence reset: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "message": f"An unexpected error occurred during sequence reset: {str(e)}",
                    "status_code": 500
                }
            )
    
    def get_sequence_status(self, admin_user_payload: dict, db: Session) -> Dict[str, Any]:
        """
        Get the current status of all sequences without making any changes.
        
        This is a read-only operation that shows which sequences might be out of sync
        with their corresponding table data.
        
        Args:
            admin_user_payload: JWT payload containing admin user information
            db: SQLAlchemy database session
            
        Returns:
            Dictionary containing sequence status information
            
        Raises:
            HTTPException: If user is not authorized or if there's an error during status check
        """
        try:
            # Log the operation attempt
            admin_user_id = admin_user_payload.get('sub', 'unknown')
            admin_username = admin_user_payload.get('preferred_username', 'unknown')
            
            logger.info(f"Sequence status check requested by admin user: {admin_username} (ID: {admin_user_id})")
            
            # Get the sequence status
            result = get_sequence_status(db)
            
            # Log the completion
            if result.get('success', False):
                total_sequences = result.get('total_sequences', 0)
                out_of_sync_count = result.get('out_of_sync_count', 0)
                logger.info(
                    f"Sequence status check completed by admin {admin_username}. "
                    f"Total: {total_sequences}, Out of sync: {out_of_sync_count}"
                )
            else:
                logger.error(f"Sequence status check failed for admin {admin_username}")
            
            return result
            
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error during sequence status check: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "message": f"An unexpected error occurred during sequence status check: {str(e)}",
                    "status_code": 500
                }
            )
    
    def validate_admin_permissions(self, admin_user_payload: dict) -> bool:
        """
        Validate that the user has admin permissions for database operations.
        
        Args:
            admin_user_payload: JWT payload containing admin user information
            
        Returns:
            True if user has valid admin permissions
            
        Raises:
            HTTPException: If user doesn't have required permissions
        """
        try:
            # Check if user has admin role or specific database admin permissions
            user_roles = admin_user_payload.get('cognito:groups', [])
            
            # For now, we'll allow any authenticated admin user
            # In the future, you might want to check for specific roles like 'database_admin'
            if not admin_user_payload.get('sub'):
                raise HTTPException(
                    status_code=401,
                    detail={
                        "message": "Invalid admin token: missing user ID",
                        "status_code": 401
                    }
                )
            
            logger.info(f"Admin permissions validated for user: {admin_user_payload.get('preferred_username', 'unknown')}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error validating admin permissions: {str(e)}")
            raise HTTPException(
                status_code=403,
                detail={
                    "message": "Failed to validate admin permissions",
                    "status_code": 403
                }
            )
