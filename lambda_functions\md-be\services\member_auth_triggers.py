import logging
import requests
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from jose import jwt
from models.member import CoM<PERSON>ber, CoAuth0User
from config import settings
from utils.response_utils import created_response, success_response
from utils.jwt_validator import validate_jwt_token
from routes.member_authentication_router import PostLoginRequest

logger = logging.getLogger(__name__)

def post_registration_trigger(email: str, user_id: str, db: Session):
    """
    Post-registration trigger: Create member in DB using email and user_id from Auth0 post-registration action
    Called directly from Auth0 post-registration action with user details
    """
    try:
        # Validate input parameters
        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Check if member already exists
        existing_member = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if existing_member:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

        # Create CoAuth0User record with basic info from Auth0
        auth0_user = CoAuth0User(
            userId=user_id,
            email=email,
            name="",  # Will be updated when user logs in
            familyName=None,
            givenName=None,
            nickName="",
            picture="",
            emailVerified=False,  # Will be updated from Auth0
            identities=[],
            locale=None,
            userMetadata={},
            appMetadata={},
            lastIp="",
            createdAt=None,
            updatedAt=None
        )

        # Create CoMember record
        member = CoMember(
            auth0Id=user_id,
            loginEmail=email,
            firstName=None,
            lastName=None,
            loginEmailVerified=False
        )

        db.add(auth0_user)
        db.add(member)
        db.commit()
        db.refresh(member)
        db.refresh(auth0_user)

        # Set self-referential created/updated fields
        member.createdByMember = member.uuid
        member.updatedByMember = member.uuid
        db.commit()
        db.refresh(member)

        logger.info(f"Created new member: {member.uuid} for Auth0 ID: {user_id}")

        return created_response(
            "Member created successfully",
            {
                "member": {
                    "uuid": str(member.uuid),
                    "email": member.loginEmail,
                    "auth0_id": member.auth0Id
                }
            }
        )
        
    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.exception("Database error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Database error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
    except Exception as e:
        logger.exception("Unexpected error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def post_login_trigger(request: PostLoginRequest, db: Session):
    """
    Post-login trigger: Update member data using info from Auth0 post-login action
    Called directly from Auth0 post-login action with user details
    """
    try:
        # Extract data from request
        email = request.email
        user_id = request.user_id
        name = request.name
        last_login = request.last_login

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Get member from database
        member = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Member not found. Please register first.",
                    "status_code": status.HTTP_404_NOT_FOUND
                }
            )

        # Update Auth0 user data if exists
        auth0_user = db.query(CoAuth0User).filter_by(userId=user_id).first()
        if auth0_user:
            auth0_user.email = email
            auth0_user.name = name or auth0_user.name
            auth0_user.updatedAt = last_login
            db.commit()

        # Update member data
        member.loginEmail = email
        if name:
            # Try to split name into first and last name
            name_parts = name.split(' ', 1)
            if len(name_parts) >= 1:
                member.firstName = name_parts[0]
            if len(name_parts) >= 2:
                member.lastName = name_parts[1]

        db.commit()
        db.refresh(member)

        logger.info(f"Successful login for member: {member.uuid}")

        return success_response(
            "Login successful",
            {
                "member": {
                    "uuid": str(member.uuid),
                    "email": member.loginEmail,
                    "auth0_id": member.auth0Id,
                    "first_name": member.firstName,
                    "last_name": member.lastName
                },
                "last_login": last_login
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during post-login")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during login",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
