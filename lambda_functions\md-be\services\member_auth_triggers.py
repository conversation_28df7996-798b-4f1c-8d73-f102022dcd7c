import logging
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from jose import jwt
from models.member import CoMember, CoAuth0User
from config import settings
from utils.response_utils import created_response, success_response
from utils.jwt_validator import validate_jwt_token

logger = logging.getLogger(__name__)

def post_registration_trigger(access_token: str, db: Session):
    """
    Post-registration trigger: Verify user with Auth0 JWKS and add member to DB
    Called after user completes registration on Auth0 Universal Login
    """
    try:
        # Verify token using existing JWT validator
        payload = validate_jwt_token(
            token=access_token,
            jwks_url=settings.JWKS_URL,
            issuer=settings.ISSUER,
            audience=settings.AUDIENCE
        )
        
        auth0_id = payload.get("sub")
        email = payload.get("email")
        
        if not auth0_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid token: missing user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )
        
        # Check if member already exists
        existing_member = db.query(CoMember).filter_by(auth0Id=auth0_id).first()
        if existing_member:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )
        
        # Create CoAuth0User record
        auth0_user = CoAuth0User(
            userId=auth0_id,
            email=email,
            name=payload.get("name", ""),
            familyName=payload.get("family_name"),
            givenName=payload.get("given_name"),
            nickName=payload.get("nickname", ""),
            picture=payload.get("picture", ""),
            emailVerified=payload.get("email_verified", False),
            identities=payload.get("identities", []),
            locale=payload.get("locale"),
            userMetadata=payload.get("user_metadata", {}),
            appMetadata=payload.get("app_metadata", {}),
            lastIp=payload.get("last_ip", ""),
            createdAt=payload.get("created_at"),
            updatedAt=payload.get("updated_at")
        )
        
        # Create CoMember record
        member = CoMember(
            auth0Id=auth0_id,
            loginEmail=email,
            firstName=payload.get("given_name"),
            lastName=payload.get("family_name"),
            loginEmailVerified=payload.get("email_verified", False)
        )
        
        db.add(auth0_user)
        db.add(member)
        db.commit()
        db.refresh(member)
        db.refresh(auth0_user)
        
        # Set self-referential created/updated fields
        member.createdByMember = member.uuid
        member.updatedByMember = member.uuid
        db.commit()
        db.refresh(member)
        
        logger.info(f"Created new member: {member.uuid} for Auth0 ID: {auth0_id}")
        
        return created_response(
            "Member created successfully",
            {
                "member": {
                    "uuid": str(member.uuid),
                    "email": member.loginEmail,
                    "auth0_id": member.auth0Id
                }
            }
        )
        
    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.exception("Database error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Database error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
    except Exception as e:
        logger.exception("Unexpected error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def post_login_trigger(access_token: str, db: Session):
    """
    Post-login trigger: Verify user with Auth0 JWKS and return access token
    Called after user completes login on Auth0 Universal Login
    """
    try:
        # Verify token using existing JWT validator
        payload = validate_jwt_token(
            token=access_token,
            jwks_url=settings.JWKS_URL,
            issuer=settings.ISSUER,
            audience=settings.AUDIENCE
        )
        
        auth0_id = payload.get("sub")
        
        if not auth0_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid token: missing user ID",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )
        
        # Get member from database
        member = db.query(CoMember).filter_by(auth0Id=auth0_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Member not found. Please register first.",
                    "status_code": status.HTTP_404_NOT_FOUND
                }
            )
        
        # Update Auth0 user data if exists
        auth0_user = db.query(CoAuth0User).filter_by(userId=auth0_id).first()
        if auth0_user:
            auth0_user.email = payload.get("email", auth0_user.email)
            auth0_user.name = payload.get("name", auth0_user.name)
            auth0_user.emailVerified = payload.get("email_verified", auth0_user.emailVerified)
            auth0_user.updatedAt = payload.get("updated_at", auth0_user.updatedAt)
            db.commit()
        
        logger.info(f"Successful login for member: {member.uuid}")
        
        return success_response(
            "Login successful",
            {
                "member": {
                    "uuid": str(member.uuid),
                    "email": member.loginEmail,
                    "auth0_id": member.auth0Id,
                    "membership_tier": member.membershipTier,
                    "community_status": member.communityStatus
                },
                "access_token": access_token
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during post-login")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during login",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
