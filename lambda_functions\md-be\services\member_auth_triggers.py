import logging
import requests
from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from jose import jwt
from models.member import Co<PERSON>ember, CoAuth0User
from config import settings
from utils.response_utils import created_response, success_response
from utils.jwt_validator import validate_jwt_token

logger = logging.getLogger(__name__)

def _exchange_code_for_tokens(authorization_code: str):
    """
    Exchange authorization code for access and ID tokens
    """
    token_url = f"https://{settings.DOMAIN}/oauth/token"

    payload = {
        "grant_type": "authorization_code",
        "client_id": settings.CLIENT_ID,
        "client_secret": settings.CLIENT_SECRET,
        "code": authorization_code,
        "redirect_uri": settings.REDIRECT_URI
    }

    headers = {"Content-Type": "application/json"}
    response = requests.post(token_url, json=payload, headers=headers)

    if response.status_code != 200:
        logger.error(f"Token exchange failed: {response.status_code} - {response.text}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "Failed to exchange authorization code for tokens",
                "status_code": status.HTTP_400_BAD_REQUEST
            }
        )

    return response.json()

def post_registration_trigger(email: str, user_id: str, db: Session):
    """
    Post-registration trigger: Create member in DB using email and user_id from Auth0 post-registration action
    Called directly from Auth0 post-registration action with user details
    """
    try:
        # Validate input parameters
        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Check if member already exists
        existing_member = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if existing_member:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

        # Create CoAuth0User record with basic info from Auth0
        auth0_user = CoAuth0User(
            userId=user_id,
            email=email,
            name="",  # Will be updated when user logs in
            familyName=None,
            givenName=None,
            nickName="",
            picture="",
            emailVerified=False,  # Will be updated from Auth0
            identities=[],
            locale=None,
            userMetadata={},
            appMetadata={},
            lastIp="",
            createdAt=None,
            updatedAt=None
        )

        # Create CoMember record
        member = CoMember(
            auth0Id=user_id,
            loginEmail=email,
            firstName=None,
            lastName=None,
            loginEmailVerified=False
        )

        db.add(auth0_user)
        db.add(member)
        db.commit()
        db.refresh(member)
        db.refresh(auth0_user)

        # Set self-referential created/updated fields
        member.createdByMember = member.uuid
        member.updatedByMember = member.uuid
        db.commit()
        db.refresh(member)

        logger.info(f"Created new member: {member.uuid} for Auth0 ID: {user_id}")

        return created_response(
            "Member created successfully",
            {
                "member": {
                    "uuid": str(member.uuid),
                    "email": member.loginEmail,
                    "auth0_id": member.auth0Id
                }
            }
        )
        
    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.exception("Database error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Database error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
    except Exception as e:
        logger.exception("Unexpected error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def post_login_trigger(authorization_code: str, db: Session):
    """
    Post-login trigger: Exchange code for tokens, verify with Auth0 JWKS and return access token
    Called after user completes login on Auth0 Universal Login
    """
    try:
        # Exchange authorization code for tokens
        tokens = _exchange_code_for_tokens(authorization_code)
        access_token = tokens.get("access_token")
        id_token = tokens.get("id_token")

        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "No access token received from Auth0",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Verify access token using existing JWT validator
        payload = validate_jwt_token(
            token=access_token,
            jwks_url=settings.JWKS_URL,
            issuer=settings.ISSUER,
            audience=settings.AUDIENCE
        )

        # Also get user info from ID token if available
        if id_token:
            id_payload = jwt.get_unverified_claims(id_token)
            # Merge ID token claims for more complete user info
            payload.update({k: v for k, v in id_payload.items() if k not in payload or not payload[k]})
        
        auth0_id = payload.get("sub")
        
        if not auth0_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid token: missing user ID",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )
        
        # Get member from database
        member = db.query(CoMember).filter_by(auth0Id=auth0_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Member not found. Please register first.",
                    "status_code": status.HTTP_404_NOT_FOUND
                }
            )
        
        # Update Auth0 user data if exists
        auth0_user = db.query(CoAuth0User).filter_by(userId=auth0_id).first()
        if auth0_user:
            auth0_user.email = payload.get("email", auth0_user.email)
            auth0_user.name = payload.get("name", auth0_user.name)
            auth0_user.emailVerified = payload.get("email_verified", auth0_user.emailVerified)
            auth0_user.updatedAt = payload.get("updated_at", auth0_user.updatedAt)
            db.commit()
        
        logger.info(f"Successful login for member: {member.uuid}")
        
        return success_response(
            "Login successful",
            {
                "member": {
                    "uuid": str(member.uuid),
                    "email": member.loginEmail,
                    "auth0_id": member.auth0Id,
                },
                "access_token": access_token
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during post-login")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during login",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
