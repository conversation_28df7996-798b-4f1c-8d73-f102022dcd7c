import logging
from fastapi import HTT<PERSON>Exception, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from models.member import CoMember, CoAuth0User
from utils.response_utils import created_response, success_response

logger = logging.getLogger(__name__)

def post_registration_trigger(request, db: Session):
    try:
        email = request.get("email")
        user_id = request.get("user_id")

        # Validate input parameters
        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Check if member already exists
        existing_member = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if existing_member:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

        # Create CoAuth0User record with basic info from Auth0
        auth0_user = CoAuth0User(
            userId=user_id,
            email=email,
            name=request.get("name") or None,
            familyName=request.get("family_name") or None,
            givenName=request.get("given_name") or None,
            nickName=request.get("nickname") or None,
            picture=request.get("picture") or None,
            emailVerified=request.get("email_verified") or False,
            identities= request.get("identities") or [],
            locale= request.get("locale") or None,
            userMetadata={},
            appMetadata={},
            lastIp=request.get("last_ip") or None,
            lastLogin=request.get("last_login") or None,
            createdAt= request.get("created_at") or None,
            updatedAt= request.get("updated_at") or None,
            loginsCount=request.get("logins_count") or 0
        )

        # Create CoMember record
        member = CoMember(
            auth0Id=user_id,
            loginEmail=email,
            firstName= request.get("name") or None,
            lastName= request.get("family_name") or None,
            loginEmailVerified=request.get("email_verified") or False
        )

        db.add(auth0_user)
        db.add(member)
        db.commit()
        db.refresh(member)
        db.refresh(auth0_user)

        # Set self-referential created/updated fields
        member.createdByMember = member.uuid
        member.updatedByMember = member.uuid
        db.commit()
        db.refresh(member)

        logger.info(f"Created new member: {member.uuid} for Auth0 ID: {user_id}")

        return created_response(
            "Member created successfully",
            {}
        )
        
    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.exception("Database error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Database error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
    except Exception as e:
        logger.exception("Unexpected error during post-registration")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def post_login_trigger(request, db: Session):
    try:
        # Extract data from request
        email = request.get("email")
        user_id = request.get("user_id")
        last_login = request.get("last_login")

        if not user_id or not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Missing required user ID or email",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        # Get member from database
        member = db.query(CoMember).filter_by(auth0Id=user_id).first()
        if not member:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "message": "Member not found. Please register first.",
                    "status_code": status.HTTP_404_NOT_FOUND
                }
            )
        # Update last login time
        member.lastLogin = last_login or member.lastLogin
        db.commit()

        return success_response(
            "Login successful",
            {}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Unexpected error during post-login")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during login",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
