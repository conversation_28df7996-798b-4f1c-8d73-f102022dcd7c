AUTH0 LOGIN AND MEMBER INFO FLOW
================================

1. AUTH0 TRIGGER AND LOGIN FLOW
------------------------------
a. When user logs in through Auth0 Universal Login, Auth0 trigger runs:
   - For first-time login (logins_count === 1) or social login:
     * Automatically registers user in database
     * Endpoint called: https://stage-api.co-nomi.app/api/members/post-registration
     * Data sent: email, user_id, name, and all Auth0 profile data
   
   - For all logins (including first time):
     * Endpoint called: https://stage-api.co-nomi.app/api/members/post-login
     * Data sent: email, user_id, name, last_login

b. Backend processes post-login:
   - For first login: Creates member record in database
   - For all logins: Updates member's last login time
   - Returns success response

Note: Two important registration behaviors:
1. When you register (signup) through Auth0, it automatically performs one login immediately after registration
2. During this automatic first login, the Auth0 trigger detects logins_count === 1 and registers the user in our database
3. No manual login is required after registration - the process is seamless

2. GETTING MEMBER INFO AND UUID
------------------------------
a. After successful login, frontend must call:
   - Endpoint: GET /api/members/get-member-info
   - Headers Required: Authorization Bearer token from Auth0
   - Response includes:
     * member UUID
     * member details (email, name, etc.)

3. UPDATING MEMBER INFORMATION
------------------------------
a. With UUID obtained, use update endpoint (eg.):
   - Endpoint: PUT /api/members/uuid/{uuid}
   - Headers Required: Authorization Bearer token
   - Request Body Example:
     {
       "firstName": "John",
       "lastName": "Doe",
       "phoneNumber": "+1234567890",
     }
   - Response: Member Updated Successfully along with member details

4. SAMPLE FLOW WITH RESPONSES
------------------------------

1) Get Member Info Response:
   {
     "success": true,
     "data": {
       "uuid": "550e8400-e29b-41d4-a716-************",
       "loginEmail": "<EMAIL>",
       "firstName": "John",
       "lastName": "Doe",
       "createdAt": "2024-08-07T10:00:00Z",
       ...
     }
   }

2) Update Member Response:
   {
     "success": true,
     "member": {
       "uuid": "550e8400-e29b-41d4-a716-************",
       "firstName": "John",
       "lastName": "Doe",
       "phone": "+1234567890",
       "dateUpdated": "2024-08-07T10:30:00Z"
     }
   }

5. ERROR HANDLING
------------------------------
- 400 Bad Request: Missing required fields
- 401 Unauthorized: Invalid/expired token
- 404 Not Found: Member UUID not found
- 409 Conflict: User already exists (during registration)
- 429 Too Many Requests: Rate limit exceeded
- 500 Internal Server Error: Server-side issues

6. SECURITY NOTES
------------------------------
- All endpoints require valid Auth0 JWT token
- Token must be included in Authorization header
- Member UUID is required for all member-specific operations
