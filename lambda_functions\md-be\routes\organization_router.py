from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.orm import Session
from controller.organization_controller import OrganizationController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.organization import *
from typing import List, Optional

router = APIRouter()
organizationController = OrganizationController()

# ========================
# Organization CRUD Endpoints
# ========================

@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_organization(
    organization: OrganizationCreate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create a new organization"""
    return organizationController.create_organization(organization, user, db)

@router.get("/", dependencies=[Depends(validate_jwt_token)])
async def get_all_organizations(
    db: Session = Depends(get_db),
    # Pagination
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    # Basic filters
    name: Optional[str] = Query(None, description="Filter by organization name (partial match)"),
    city: Optional[str] = Query(None, description="Filter by city"),
    state: Optional[str] = Query(None, description="Filter by state"),
    zip: Optional[str] = Query(None, description="Filter by ZIP code"),
    companySize: Optional[str] = Query(None, description="Filter by company size"),
    industry: Optional[str] = Query(None, description="Filter by industry"),
    yearFounded: Optional[str] = Query(None, description="Filter by year founded"),
    phone: Optional[str] = Query(None, description="Filter by phone number"),
    email: Optional[str] = Query(None, description="Filter by email address"),
    # Date range filters
    dateCreatedFrom: Optional[str] = Query(None, description="Filter organizations created after this date (YYYY-MM-DD)"),
    dateCreatedTo: Optional[str] = Query(None, description="Filter organizations created before this date (YYYY-MM-DD)"),
    # Member count filter
    memberCount: Optional[int] = Query(None, description="Filter by number of members"),
    # Sorting
    sortBy: Optional[str] = Query("dateUpdated", description="Sort by field (dateUpdated, name, dateCreated, etc.)"),
    sortOrder: Optional[str] = Query("desc", description="Sort order (asc, desc)"),
    # Revenue range filters
    annualRevenueMin: Optional[str] = Query(None, description="Minimum annual revenue"),
    annualRevenueMax: Optional[str] = Query(None, description="Maximum annual revenue"),
    # Year founded range filters
    yearFoundedMin: Optional[str] = Query(None, description="Minimum year founded"),
    yearFoundedMax: Optional[str] = Query(None, description="Maximum year founded")
):
    """Get all organizations with comprehensive filtering and pagination"""
    return organizationController.get_all_organizations(
        db, page, pageSize, name, city, state, zip, companySize, industry,
        yearFounded, phone, email, dateCreatedFrom, dateCreatedTo, memberCount,
        sortBy, sortOrder, annualRevenueMin, annualRevenueMax,
        yearFoundedMin, yearFoundedMax
    )

# ========================
# Organization Verified Data Endpoints
# ========================

@router.post("/verified-data", status_code=status.HTTP_201_CREATED, dependencies=[Depends(validate_jwt_token)])
async def create_organization_verified_data(
    verified_data: OrganizationVerifiedDataCreate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create verified data for an organization"""
    return organizationController.create_organization_verified_data(verified_data, user, db)

@router.get("/verified-data/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_organization_verified_data_by_uuid(uuid: str, db: Session = Depends(get_db)):
    """Get verified data by UUID"""
    return organizationController.get_organization_verified_data_by_uuid(uuid, db)

@router.put("/verified-data/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def update_organization_verified_data_by_uuid(
    uuid: str,
    verified_data: OrganizationVerifiedDataUpdate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Update verified data by UUID"""
    return organizationController.update_organization_verified_data_by_uuid(uuid, verified_data, user, db)

@router.delete("/verified-data/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def delete_organization_verified_data_by_uuid(
    uuid: str,
    db: Session = Depends(get_db)
):
    """Delete verified data by UUID"""
    return organizationController.delete_organization_verified_data_by_uuid(uuid, db)

# ========================
# Member Organization Relations Endpoints
# ========================

@router.post("/relations", status_code=status.HTTP_201_CREATED, dependencies=[Depends(validate_jwt_token)])
async def create_member_organization_relation(
    relation: MemberOrganizationRelationCreate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Create a member-organization relation"""
    return organizationController.create_member_organization_relation(relation, user, db)

@router.get("/relations", dependencies=[Depends(validate_jwt_token)])
async def get_member_organization_relations(
    db: Session = Depends(get_db),
    memberUuid: Optional[str] = Query(None, alias="memberUuid"),
    organizationUuid: Optional[str] = Query(None, alias="organizationUuid")
):
    """Get member-organization relations with optional filtering"""
    return organizationController.get_member_organization_relations(db, memberUuid, organizationUuid)

@router.delete("/relations/", dependencies=[Depends(validate_jwt_token)])
async def delete_member_organization_relation_by_uuid(
    relation: MemberOrganizationRelationCreate,
    db: Session = Depends(get_db)
):
    """Delete a member-organization relation by UUID"""
    return organizationController.delete_member_organization_relation_by_uuid(relation, db)

# ========================
# Member-Organization Relationship Queries
# ========================

@router.get("/member/{memberUuid}/organizations", dependencies=[Depends(validate_jwt_token)])
async def get_organizations_by_member(memberUuid: str, db: Session = Depends(get_db)):
    """Get all organizations for a specific member"""
    return organizationController.get_organizations_by_member(memberUuid, db)

# ========================
# Bulk Operations
# ========================

@router.post("/bulk", status_code=status.HTTP_201_CREATED, dependencies=[Depends(validate_jwt_token)])
async def bulk_create_organizations(
    organizations: List[OrganizationCreate],
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Bulk create organizations"""
    return organizationController.bulk_create_organizations(organizations, user, db)

# ========================
# Search Endpoints
# ========================

@router.get("/search/{query}", dependencies=[Depends(validate_jwt_token)])
async def search_organizations(
    query: str,
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Search organizations by name, industry, or other fields"""
    return organizationController.search_organizations(query, db, perPage, pageSize)
# ========================
# Organization UUID-based Endpoints (must come after specific routes)
# ========================

@router.get("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_organization_by_uuid(uuid: str, db: Session = Depends(get_db)):
    """Get organization by UUID"""
    return organizationController.get_organization_by_uuid(uuid, db)

@router.put("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def update_organization(
    uuid: str,
    organization: OrganizationUpdate,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Update organization by UUID"""
    return organizationController.update_organization(uuid, organization, user, db)

@router.delete("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def delete_organization(
    uuid: str,
    user: dict = Depends(validate_jwt_token),
    db: Session = Depends(get_db)
):
    """Delete organization by UUID"""
    return organizationController.delete_organization(uuid, user, db)

# ========================
# Organization Relationship Endpoints
# ========================

@router.get("/{uuid}/verified-data", dependencies=[Depends(validate_jwt_token)])
async def get_organization_with_verified_data(uuid: str, db: Session = Depends(get_db)):
    """Get organization with its verified data"""
    return organizationController.get_organization_with_verified_data(uuid, db)

@router.get("/{uuid}/relations", dependencies=[Depends(validate_jwt_token)])
async def get_organization_with_relations(uuid: str, db: Session = Depends(get_db)):
    """Get organization with its member relations"""
    return organizationController.get_organization_with_relations(uuid, db)

@router.get("/{organizationUuid}/members", dependencies=[Depends(validate_jwt_token)])
async def get_members_by_organization(
    organizationUuid: str,
    db: Session = Depends(get_db),
    perPage: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Get all members for a specific organization with pagination"""
    return organizationController.get_members_by_organization(organizationUuid, db, perPage, pageSize)

