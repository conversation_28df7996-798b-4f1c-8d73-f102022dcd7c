from fastapi import APIRouter, Depends, status, HTTPException, Query
from sqlalchemy.orm import Session
from controller.member_authentication_controller_v2 import member_auth_controller_v2
from db.db import get_db
from schemas.member_auth_v2 import (
    AuthInitiationRequest, 
    AuthInitiationResponse,
    AuthCallbackRequest,
    AuthenticationResponse,
    TokenValidationRequest,
    DirectLoginRequest,
    AuthStatusResponse,
    ErrorResponse,
    LegacyLoginRequest,
    LegacyProvider
)
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

# ==================== ENHANCED AUTHENTICATION ENDPOINTS ====================

@router.post("/v2/auth/initiate", 
             response_model=AuthenticationResponse,
             status_code=status.HTTP_200_OK,
             summary="Initiate Authentication Flow",
             description="Step 1: Generate Auth0 authentication URL for frontend redirect")
async def initiate_authentication(request: AuthInitiationRequest):
    """
    **Step 1 of Authentication Flow: User Starts Authentication**
    
    This endpoint generates the Auth0 authentication URL that the frontend should redirect to.
    Supports multiple authentication providers including social logins.
    
    **Flow:**
    1. Frontend calls this endpoint with desired provider
    2. Backend generates Auth0 URL with proper parameters
    3. Frontend redirects user to the returned URL
    4. User authenticates with Auth0/provider
    5. Auth0 redirects back to frontend with authorization code
    
    **Supported Providers:**
    - `google`: Google OAuth2
    - `apple`: Apple Sign In
    - `linkedin`: LinkedIn OAuth2
    - `username-password`: Auth0 Database Connection
    - `auth0`: Default Auth0 Universal Login
    """
    try:
        result = member_auth_controller_v2.initiate_social_login(
            provider=request.provider,
            redirect_uri=request.redirect_uri
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in initiate_authentication endpoint")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to initiate authentication",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

@router.post("/v2/auth/callback",
             response_model=AuthenticationResponse,
             status_code=status.HTTP_200_OK,
             summary="Handle Auth0 Callback",
             description="Steps 2-5: Process Auth0 callback and complete authentication")
async def handle_auth_callback(
    request: AuthCallbackRequest,
    db: Session = Depends(get_db)
):
    """
    **Steps 2-5 of Authentication Flow: Auth0 Processing & Backend Processing**
    
    This endpoint handles the complete Auth0 callback processing:
    
    **Step 2: Auth0 Universal Login** (completed before this call)
    **Step 3: User Registers or Logs In** (completed before this call)
    **Step 4: Redirect Back to Frontend with Code** (completed before this call)
    **Step 5: Backend Receives Authorization Code** (this endpoint)
    
    **Processing:**
    1. Validates CSRF state parameter
    2. Exchanges authorization code for access/ID tokens
    3. Extracts user information from tokens
    4. Creates new user in database OR retrieves existing user
    5. Returns member info and tokens to frontend
    
    **Database Operations:**
    - Creates/updates `co_auth0_users` table record
    - Creates/updates `co_members` table record
    - Links Auth0 identity to member profile
    """
    try:
        result = member_auth_controller_v2.handle_auth_callback(
            authorization_code=request.code,
            state=request.state,
            db=db
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in handle_auth_callback endpoint")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Authentication callback processing failed",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

@router.post("/v2/auth/validate",
             response_model=AuthenticationResponse,
             status_code=status.HTTP_200_OK,
             summary="Validate and Enrich Token",
             description="Step 6: Post-login actions and token validation")
async def validate_and_enrich_token(
    request: TokenValidationRequest,
    db: Session = Depends(get_db)
):
    """
    **Step 6 of Authentication Flow: Post-Login Actions**
    
    This endpoint handles post-authentication processing:
    
    **Post-Login Actions:**
    1. Validates JWT access token
    2. Extracts user information from token
    3. Retrieves member profile from database
    4. Logs successful authentication
    5. Returns enriched user context
    
    **Use Cases:**
    - Validate tokens on protected route access
    - Refresh user context in frontend
    - Audit logging for security
    - Token-based session management
    """
    try:
        result = member_auth_controller_v2.validate_and_enrich_token(
            access_token=request.access_token,
            db=db
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in validate_and_enrich_token endpoint")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Token validation failed",
                "status_code": status.HTTP_401_UNAUTHORIZED
            }
        )

@router.post("/v2/auth/direct-login",
             response_model=AuthenticationResponse,
             status_code=status.HTTP_200_OK,
             summary="Direct Username/Password Login",
             description="Alternative direct login bypassing OAuth flow")
async def direct_login(
    request: DirectLoginRequest,
    db: Session = Depends(get_db)
):
    """
    **Direct Authentication (Alternative Flow)**
    
    This endpoint provides direct username/password authentication,
    bypassing the OAuth authorization code flow.
    
    **Use Cases:**
    - Mobile app direct login
    - API-only authentication
    - Testing and development
    - Legacy system integration
    
    **Security Notes:**
    - Still uses Auth0 for credential validation
    - Returns same token format as OAuth flow
    - Supports all Auth0 security features
    """
    try:
        # Convert to legacy format for compatibility
        legacy_request = LegacyLoginRequest(
            loginEmail=request.email,
            password=request.password
        )
        
        result = member_auth_controller_v2.direct_username_password_login(
            login_request=legacy_request,
            db=db
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in direct_login endpoint")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Direct login failed",
                "status_code": status.HTTP_401_UNAUTHORIZED
            }
        )

@router.get("/v2/auth/status",
            response_model=AuthStatusResponse,
            status_code=status.HTTP_200_OK,
            summary="Get Authentication Service Status",
            description="Get current authentication service configuration and status")
async def get_auth_status():
    """
    **Authentication Service Status**
    
    Returns current configuration and status of the authentication service.
    
    **Information Provided:**
    - Service health status
    - Auth0 configuration details
    - Supported authentication providers
    - OAuth flow type and version
    - Available endpoints and features
    """
    try:
        result = member_auth_controller_v2.get_auth_status()
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in get_auth_status endpoint")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Failed to get authentication status",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

# ==================== LEGACY COMPATIBILITY ENDPOINTS ====================

@router.post("/v2/auth/legacy/social-login",
             response_model=AuthenticationResponse,
             status_code=status.HTTP_200_OK,
             summary="Legacy Social Login (Compatibility)",
             description="Legacy endpoint for backward compatibility")
async def legacy_social_login(request: LegacyProvider):
    """
    **Legacy Compatibility Endpoint**
    
    Maintains backward compatibility with existing frontend implementations.
    Maps legacy request format to new authentication flow.
    """
    try:
        # Convert legacy format to new format
        new_request = AuthInitiationRequest(
            provider=request.provider,
            redirect_uri=request.redirect_uri
        )
        
        result = member_auth_controller_v2.initiate_social_login(
            provider=new_request.provider,
            redirect_uri=new_request.redirect_uri
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in legacy_social_login endpoint")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Legacy social login failed",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

@router.get("/v2/auth/legacy/callback",
            response_model=AuthenticationResponse,
            status_code=status.HTTP_200_OK,
            summary="Legacy Callback (Compatibility)",
            description="Legacy callback endpoint for backward compatibility")
async def legacy_callback(
    code: str = Query(..., description="Authorization code from Auth0"),
    state: str = Query(..., description="State parameter for CSRF protection"),
    db: Session = Depends(get_db)
):
    """
    **Legacy Compatibility Callback**
    
    Handles Auth0 callbacks using query parameters instead of request body.
    Maintains compatibility with existing Auth0 redirect configurations.
    """
    try:
        # Convert query params to request format
        request = AuthCallbackRequest(code=code, state=state)
        
        result = member_auth_controller_v2.handle_auth_callback(
            authorization_code=request.code,
            state=request.state,
            db=db
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("Error in legacy_callback endpoint")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Legacy callback processing failed",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
