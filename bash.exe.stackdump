Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FEBA
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210285FF9, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBB0  0002100690B4 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBE90  00021006A49D (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD93DB0000 ntdll.dll
7FFD93200000 KERNEL32.DLL
7FFD91190000 KERNELBASE.dll
7FFD92EA0000 USER32.dll
7FFD91750000 win32u.dll
7FFD932D0000 GDI32.dll
7FFD90EA0000 gdi32full.dll
7FFD916B0000 msvcp_win.dll
7FFD91780000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD939C0000 advapi32.dll
7FFD92990000 msvcrt.dll
7FFD93AF0000 sechost.dll
7FFD918A0000 bcrypt.dll
7FFD92D80000 RPCRT4.dll
7FFD90640000 CRYPTBASE.DLL
7FFD90FD0000 bcryptPrimitives.dll
7FFD92950000 IMM32.DLL
