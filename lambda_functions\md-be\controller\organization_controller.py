from sqlalchemy.orm import Session
from models.organization import CoOrganization, CoOrganizationVerifiedData, CoRelationsMembersOrganizations, CoMembersAwards
from schemas.organization import *
from services import organization_api
from typing import List, Optional

class OrganizationController:
    
    # ========================
    # Organization CRUD Operations
    # ========================
    
    def create_organization(self, organization: OrganizationCreate, user: dict, db: Session):
        """Create a new organization"""
        return organization_api.create_organization(organization, user, db)
    
    def create_organization_by_member(self, organization: OrganizationCreate, user: dict, db: Session):
        return organization_api.create_organization_by_member(organization, user, db)
    
    def get_all_organizations(self, db: Session, page: int = 1, pageSize: int = 10,
                            name: Optional[str] = None, city: Optional[str] = None,
                            state: Optional[str] = None, zip: Optional[str] = None,
                            companySize: Optional[str] = None, industry: Optional[str] = None,
                            yearFounded: Optional[str] = None, phone: Optional[str] = None,
                            email: Optional[str] = None, dateCreatedFrom: Optional[str] = None,
                            dateCreatedTo: Optional[str] = None, memberCount: Optional[int] = None,
                            sortBy: str = "dateUpdated", sortOrder: str = "desc",
                            annualRevenueMin: Optional[str] = None, annualRevenueMax: Optional[str] = None,
                            yearFoundedMin: Optional[str] = None, yearFoundedMax: Optional[str] = None):
        """Get all organizations with comprehensive filtering and pagination"""
        return organization_api.get_all_organizations(
            db, page, pageSize, name, city, state, zip, companySize, industry,
            yearFounded, phone, email, dateCreatedFrom, dateCreatedTo, memberCount,
            sortBy, sortOrder, annualRevenueMin, annualRevenueMax,
            yearFoundedMin, yearFoundedMax
        )
        
    
    def get_organization_by_uuid(self, uuid: str, db: Session):
        """Get organization by UUID"""
        return organization_api.get_organization_by_uuid(uuid, db)
    
    def update_organization(self, uuid: str, organization: OrganizationUpdate, user: dict, db: Session):
        """Update organization by UUID"""
        return organization_api.update_organization(uuid, organization, user, db)
    
    def delete_organization(self, uuid: str, user: dict, db: Session):
        """Delete organization by UUID"""
        return organization_api.delete_organization(uuid, user, db)
    
    def get_organization_with_verified_data(self, uuid: str, db: Session):
        """Get organization with verified data"""
        return organization_api.get_organization_with_verified_data(uuid, db)
    
    def get_organization_with_relations(self, uuid: str, db: Session):
        """Get organization with member relations and awards"""
        return organization_api.get_organization_with_relations(uuid, db)
    
    # ========================
    # Organization Verified Data CRUD Operations
    # ========================
    
    def create_organization_verified_data(self, verified_data: OrganizationVerifiedDataCreate, user: dict, db: Session):
        """Create verified data for an organization"""
        return organization_api.create_organization_verified_data(verified_data, user, db)
    
    def get_organization_verified_data_by_uuid(self, uuid: str, db: Session):
        """Get verified data by UUID"""
        return organization_api.get_organization_verified_data_by_uuid(uuid, db)
    
    def update_organization_verified_data_by_uuid(self, uuid: str, verified_data: OrganizationVerifiedDataUpdate, user: dict, db: Session):
        """Update verified data by UUID"""
        return organization_api.update_organization_verified_data_by_uuid(uuid, verified_data, user, db)
    
    def delete_organization_verified_data_by_uuid(self, uuid: str, db: Session):
        """Delete verified data by UUID"""
        return organization_api.delete_organization_verified_data_by_uuid(uuid, db)
    
    # ========================
    # Member Organization Relations CRUD Operations
    # ========================
    
    def create_member_organization_relation(self, relation: MemberOrganizationRelationCreate, user: dict, db: Session):
        """Create a member-organization relation"""
        return organization_api.create_member_organization_relation(relation, user, db)
    
    def get_member_organization_relations(self, db: Session, memberid: Optional[str] = None, organizationid: Optional[str] = None):
        """Get member-organization relations with optional filtering"""
        return organization_api.get_member_organization_relations(db, memberid, organizationid)
    
    def delete_member_organization_relation_by_uuid(self, relation: MemberOrganizationRelationCreate, db: Session):
        """Delete a member-organization relation by UUID"""
        return organization_api.delete_member_organization_relation_by_uuid(relation, db)
    
    # ========================
    # Member Awards CRUD Operations
    # ========================
    
    def create_member_award(self, award: MemberAwardCreate, user: dict, db: Session):
        """Create a member award"""
        return organization_api.create_member_award(award, user, db)
    
    def get_member_awards(self, db: Session, memberid: Optional[str] = None, organizationid: Optional[str] = None, perPage: int = 1, pageSize: int = 10):
        """Get member awards with optional filtering"""
        return organization_api.get_member_awards(db, memberid, organizationid, perPage, pageSize)
    
    def get_member_award_by_uuid(self, uuid: str, db: Session):
        """Get a specific member award by UUID"""
        return organization_api.get_member_award_by_uuid(uuid, db)
    
    def update_member_award_by_uuid(self, uuid: str, award: MemberAwardUpdate, user: dict, db: Session):
        """Update a member award by UUID"""
        return organization_api.update_member_award_by_uuid(uuid, award, user, db)
    
    def delete_member_award_by_uuid(self, uuid: str, db: Session):
        """Delete a member award by UUID"""
        return organization_api.delete_member_award_by_uuid(uuid, db)
    
    # ========================
    # Advanced Operations
    # ========================
    
    def get_organizations_by_member(self, member_uuid: str, db: Session):
        """Get all organizations for a specific member"""
        return organization_api.get_organizations_by_member(member_uuid, db)
    
    def get_members_by_organization(self, organizationid: str, db: Session, perPage: int = 1, pageSize: int = 10):
        """Get all members for a specific organization with pagination"""
        return organization_api.get_members_by_organization(organizationid, db, perPage, pageSize)
    
    def get_awards_by_organization(self, organizationid: str, db: Session):
        """Get all awards for a specific organization"""
        return organization_api.get_awards_by_organization(organizationid, db)

    def get_awards_by_member(self, memberid: str, db: Session):
        """Get all awards for a specific member"""
        return organization_api.get_awards_by_member(memberid, db)
    
    def bulk_create_organizations(self, organizations: List[OrganizationCreate], user: dict, db: Session):
        """Bulk create organizations"""
        return organization_api.bulk_create_organizations(organizations, user, db)
    
    def search_organizations(self, query: str, db: Session, perPage: int = 1, pageSize: int = 10):
        """Search organizations by name, industry, or location"""
        return organization_api.search_organizations(query, db, perPage, pageSize)
