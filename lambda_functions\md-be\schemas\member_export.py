from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class MemberExportFilters(BaseModel):
    """Filters for member export functionality"""
    firstName: Optional[str] = Field(None, description="Filter by first name (partial match)")
    lastName: Optional[str] = Field(None, description="Filter by last name (partial match)")
    email: Optional[str] = Field(None, description="Filter by email (partial match)")
    organizationName: Optional[str] = Field(None, description="Filter by organization name (partial match)")
    membershipTier: Optional[str] = Field(None, description="Filter by membership tier")
    dateCreated: Optional[datetime] = Field(None, description="Filter by date created")
    city: Optional[str] = Field(None, description="Filter by city (partial match)")
    state: Optional[str] = Field(None, description="Filter by state (partial match)")
    zip: Optional[str] = Field(None, description="Filter by ZIP code (partial match)")
    industry: Optional[str] = Field(None, description="Filter by industry (partial match)")
    companySize: Optional[str] = Field(None, description="Filter by company size")

class MemberExportRequest(BaseModel):
    """Request schema for member export endpoint"""
    filters: Optional[MemberExportFilters] = Field(default_factory=MemberExportFilters, description="Optional filters to apply")
    selectedFields: List[str] = Field(..., description="List of fields to include in export")
    notes: str = Field(..., description="Purpose/reason for the export")

    @validator('selectedFields')
    def validate_selected_fields(cls, v):
        if not v or len(v) == 0:
            raise ValueError('At least one selected field is required')
        
        # Define available fields that can be exported
        available_fields = {
            'firstName', 'lastName', 'loginEmail', 'personalBusinessEmail', 'phone',
            'professionalTitle', 'membershipTier', 'communityStatus', 'dateCreated',
            'organizationName', 'city', 'state', 'zip', 'industry', 'companySize',
            'annualRevenue', 'yearFounded'
        }
        
        invalid_fields = set(v) - available_fields
        if invalid_fields:
            raise ValueError(f'Invalid fields: {", ".join(invalid_fields)}. Available fields: {", ".join(sorted(available_fields))}')
        
        return v

    @validator('notes')
    def validate_notes(cls, v):
        if not v or v.strip() == '':
            raise ValueError('Notes cannot be empty')
        return v.strip()

    class Config:
        from_attributes = True 