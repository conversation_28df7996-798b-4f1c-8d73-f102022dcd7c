# Auth0 Dashboard Configuration Guide

## Overview

This guide provides step-by-step instructions for configuring your Auth0 dashboard to work with the enhanced member authentication service. Follow these steps to ensure proper integration with the new authentication flow.

## Prerequisites

- Auth0 account with admin access
- Domain: `dev-gufgtjtn8u27pszl.us.auth0.com` (or your custom domain)
- Management API access

## 1. Application Configuration

### Navigate to Applications
1. Log in to your Auth0 Dashboard
2. Go to **Applications** → **Applications**
3. Select your existing application or create a new one

### Application Settings
Configure the following settings for your application:

#### Basic Information
- **Name**: `Member Authentication App V2`
- **Application Type**: `Single Page Application` (SPA) or `Regular Web Application`
- **Application Logo**: Upload your organization logo

#### Application URIs
```
Allowed Callback URLs:
https://stage-console.co-nomi.app/login
https://stage-console.co-nomi.app/auth/callback
http://localhost:3000/auth/callback (for development)

Allowed Logout URLs:
https://stage-console.co-nomi.app/logout
https://stage-console.co-nomi.app/
http://localhost:3000/ (for development)

Allowed Web Origins:
https://stage-console.co-nomi.app
http://localhost:3000 (for development)

Allowed Origins (CORS):
https://stage-console.co-nomi.app
http://localhost:3000 (for development)
```

#### Advanced Settings

**Grant Types** (Enable these):
- [x] Authorization Code
- [x] Refresh Token
- [x] Client Credentials
- [x] Password (for direct login)

**Application Metadata**:
```json
{
  "service_version": "2.0",
  "flow_type": "authorization_code_with_pkce",
  "supported_providers": ["google", "apple", "linkedin", "username-password"]
}
```

## 2. API Configuration

### Create/Configure API
1. Go to **Applications** → **APIs**
2. Create new API or select existing one

#### API Settings
```
Name: Member Authentication API V2
Identifier: https://dev-gufgtjtn8u27pszl.us.auth0.com/api/v2/
Signing Algorithm: RS256
```

#### Scopes
Add the following scopes:
```
openid - OpenID Connect authentication
profile - User profile information
email - User email address
read:members - Read member data
write:members - Write member data
```

#### Token Settings
```
Token Expiration: 3600 seconds (1 hour)
Token Expiration For Browser Flows: 3600 seconds
Allow Offline Access: Yes (for refresh tokens)
```

## 3. Social Connections Setup

### Google OAuth2
1. Go to **Authentication** → **Social**
2. Enable **Google** connection

#### Google Configuration
```
Client ID: [Your Google OAuth2 Client ID]
Client Secret: [Your Google OAuth2 Client Secret]

Scopes: email profile

Attributes:
- email (required)
- email_verified
- name
- given_name
- family_name
- picture
```

### Apple Sign In
1. Enable **Apple** connection

#### Apple Configuration
```
Services ID: [Your Apple Services ID]
Apple Team ID: [Your Apple Team ID]
Client Secret Signing Key: [Your Apple Private Key]
Key ID: [Your Apple Key ID]

Scopes: email name
```

### LinkedIn
1. Enable **LinkedIn** connection

#### LinkedIn Configuration
```
Client ID: [Your LinkedIn Client ID]
Client Secret: [Your LinkedIn Client Secret]

Scopes: r_liteprofile r_emailaddress

Attributes:
- email (required)
- name
- given_name
- family_name
- picture
```

## 4. Database Connection

### Username-Password Authentication
1. Go to **Authentication** → **Database**
2. Configure **Username-Password-Authentication** connection

#### Database Settings
```
Name: Username-Password-Authentication
Database Type: Auth0 Database

Password Policy: Good
- Minimum length: 8 characters
- At least one uppercase letter
- At least one lowercase letter  
- At least one number
- At least one special character

Password History: 5 passwords
Password Dictionary: Enabled
Brute Force Protection: Enabled
```

#### Custom Database (Optional)
If using custom database scripts:
```javascript
// Login Script
function login(email, password, callback) {
  // Your custom login logic here
  // Must call callback(null, user) on success
  // Must call callback(error) on failure
}

// Get User Script
function getByEmail(email, callback) {
  // Your custom user lookup logic here
}
```

## 5. Rules and Actions

### Create Post-Login Action
1. Go to **Actions** → **Flows** → **Login**
2. Create new action: `Enrich User Profile`

#### Action Code
```javascript
exports.onExecutePostLogin = async (event, api) => {
  const namespace = 'https://your-app.com/';
  
  // Add custom claims to tokens
  api.idToken.setCustomClaim(`${namespace}user_id`, event.user.user_id);
  api.idToken.setCustomClaim(`${namespace}email`, event.user.email);
  api.idToken.setCustomClaim(`${namespace}email_verified`, event.user.email_verified);
  
  // Add user metadata
  if (event.user.user_metadata) {
    api.idToken.setCustomClaim(`${namespace}user_metadata`, event.user.user_metadata);
  }
  
  // Add app metadata
  if (event.user.app_metadata) {
    api.accessToken.setCustomClaim(`${namespace}app_metadata`, event.user.app_metadata);
  }
};
```

### Create Pre-User Registration Action (Optional)
```javascript
exports.onExecutePreUserRegistration = async (event, api) => {
  // Validate email domain
  const allowedDomains = ['company.com', 'partner.com'];
  const emailDomain = event.user.email.split('@')[1];
  
  if (!allowedDomains.includes(emailDomain)) {
    api.access.deny('Registration not allowed for this email domain');
  }
};
```

## 6. Tenant Settings

### General Settings
1. Go to **Settings** → **General**

#### Configure:
```
Friendly Name: US Chamber of Commerce
Logo URL: https://your-logo-url.com/logo.png
Support Email: <EMAIL>
Support URL: https://your-support-url.com
```

### Advanced Settings
```
Default Directory: Username-Password-Authentication
Default Audience: https://dev-gufgtjtn8u27pszl.us.auth0.com/api/v2/
Default Scopes: openid profile email
```

## 7. Universal Login Configuration

### Branding
1. Go to **Branding** → **Universal Login**

#### Customize:
```
Logo: Upload your organization logo
Primary Color: #your-brand-color
Background Color: #ffffff
```

### Login Page Template
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <title>Sign In | US Chamber of Commerce</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
</head>
<body>
  <div id="lock" class="lock-container"></div>
  <script src="https://cdn.auth0.com/js/lock/11.30.0/lock.min.js"></script>
  <script>
    var lock = new Auth0Lock(config.clientID, config.auth0Domain, {
      auth: {
        redirectUrl: config.callbackURL,
        responseType: 'code',
        audience: config.extraParams.audience,
        params: config.extraParams
      },
      theme: {
        logo: 'https://your-logo-url.com/logo.png',
        primaryColor: '#your-brand-color'
      },
      languageDictionary: {
        title: "US Chamber of Commerce"
      },
      socialButtonStyle: 'big',
      allowedConnections: ['google-oauth2', 'apple', 'linkedin', 'Username-Password-Authentication']
    });
    
    lock.show();
  </script>
</body>
</html>
```

## 8. Security Settings

### Attack Protection
1. Go to **Security** → **Attack Protection**

#### Enable:
- [x] Brute Force Protection
- [x] Suspicious IP Throttling
- [x] Breached Password Detection

#### Configure:
```
Brute Force Protection:
- Max attempts: 10
- Block duration: 15 minutes

Suspicious IP Throttling:
- Max attempts per IP: 100
- Block duration: 24 hours
```

### Multi-Factor Authentication (Optional)
1. Go to **Security** → **Multi-factor Auth**
2. Enable desired MFA methods:
   - SMS
   - Email
   - TOTP (Google Authenticator)
   - Push notifications

## 9. Monitoring and Logs

### Log Streams
1. Go to **Monitoring** → **Streams**
2. Create log stream for your monitoring system

#### Example Configuration:
```json
{
  "name": "Application Logs",
  "type": "http",
  "sink": {
    "httpEndpoint": "https://your-logging-endpoint.com/auth0-logs",
    "httpContentType": "application/json",
    "httpAuthorization": "Bearer your-auth-token"
  },
  "filters": [
    {
      "type": "category",
      "name": "auth.login.success"
    },
    {
      "type": "category", 
      "name": "auth.login.fail"
    }
  ]
}
```

## 10. Testing Configuration

### Test Authentication Flow
1. Use Auth0's **Authentication API Debugger**
2. Test each social connection
3. Verify callback URLs work correctly
4. Test token validation

### Test Endpoints
```bash
# Test initiate authentication
curl -X POST https://your-api.com/api/members/v2/auth/initiate \
  -H "Content-Type: application/json" \
  -d '{"provider": "google"}'

# Test callback (after getting code from Auth0)
curl -X POST https://your-api.com/api/members/v2/auth/callback \
  -H "Content-Type: application/json" \
  -d '{"code": "auth_code", "state": "state_value"}'
```

## 11. Production Checklist

Before going to production, verify:

- [ ] All callback URLs are HTTPS
- [ ] Secrets are properly secured
- [ ] Rate limiting is configured
- [ ] Monitoring is set up
- [ ] Error handling is tested
- [ ] Security headers are configured
- [ ] CORS settings are restrictive
- [ ] Log retention is configured
- [ ] Backup procedures are in place
- [ ] Incident response plan is ready

## Troubleshooting

### Common Issues

1. **Callback URL Mismatch**
   - Verify URLs in Auth0 dashboard match your application
   - Check for trailing slashes and protocol differences

2. **CORS Errors**
   - Add your domain to Allowed Origins in Auth0
   - Verify preflight requests are handled

3. **Token Validation Failures**
   - Check audience and issuer configuration
   - Verify JWKS URL is accessible
   - Confirm token hasn't expired

4. **Social Login Issues**
   - Verify social provider credentials
   - Check scope permissions
   - Confirm redirect URLs with social providers

For additional support, consult the Auth0 documentation or contact Auth0 support.
