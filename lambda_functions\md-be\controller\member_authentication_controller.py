
from schemas.member import CoMemberCreate, LoginRequest, CoMemberCreateEnhanced
from services import member_authentication_service
from sqlalchemy.orm import Session

class MemberAuthenticationController:
    def create_member(self, member: CoMemberCreateEnhanced, admin_user_payload: dict, db: Session):
        new_user_and_token = member_authentication_service.register_user_in_auth0(member.loginEmail, member.password, admin_user_payload)
        return member_authentication_service.create_member(member, admin_user_payload, new_user_and_token, db)

    def login(self, payload: LoginRequest, db: Session):
        return member_authentication_service.login(payload.loginEmail, payload.password, db)

    def login_redirect(self, provider: str, redirect_uri: str):
        return member_authentication_service.login_redirect(provider, redirect_uri)

    def callback(self, code: str, db: Session):
        return member_authentication_service.callback(code, db)