import logging
from venv import create
from fastapi import HTTPException, status
import requests.compat
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from auth0_manage_api.auth0_manage import get_management_token
from models.member import <PERSON><PERSON><PERSON><PERSON>, CoAuth0User
from models.admin import AdminModel
from schemas.member import CoMemberCreate, CoMemberCreateEnhanced
from auth0.management import Auth0
import requests
from jose import jwt
from config import settings
from utils import auth_admin
from utils.response_utils import created_response, success_response

def create_member(member: CoMemberCreateEnhanced, admin_user_payload: dict, new_user_and_token: dict, db: Session):
    try:
        # Manually check required fields (optional if enforced by schema, but extra safe)
        required_fields = {
            "loginEmail": member.loginEmail,
            "password": member.password,
        }

        missing = [field for field, value in required_fields.items() if not value]
        if missing:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail={
                    "message": f"Missing required field(s): {', '.join(missing)}",
                    "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY
                }
            )

        # Check if member already exists
        if db.query(CoMember).filter_by(auth0Id=new_user_and_token['new_user']['user_id']).first():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail={
                    "message": "User already registered.",
                    "status_code": status.HTTP_409_CONFLICT
                }
            )

        # Adding data to CoMember table
        new_member = CoMember(
            auth0Id=new_user_and_token['new_user']['user_id'],
            loginEmail=member.loginEmail,
        )
        
        # Adding data to CoAuth0User table
        user = new_user_and_token["new_user"]

        new_member_auth0_users = CoAuth0User(
            userId=user["user_id"],
            email=user.get("email"),
            name=user.get("name"),
            familyName=user.get("familyName"),
            givenName=user.get("given_name"),
            nickName=user.get("nickname"),
            picture=user.get("picture"),
            emailVerified=user.get("email_verified"),
            identities=user.get("identities"),
            createdAt=user.get("created_at"),
            updatedAt=user.get("updated_at")
        )
        
        # Set created/updated by fields using UUIDs
        if admin_user_payload.get("sub"):
            new_member.createdByAdmin = admin_user_payload["sub"]
            new_member.updatedByAdmin = admin_user_payload["sub"]

        db.add(new_member)
        db.add(new_member_auth0_users)
        db.commit()
        db.refresh(new_member)
        db.refresh(new_member_auth0_users)

        # For self-registration, set the created/updated by fields to the new member's own UUID
        if not admin_user_payload.get("sub"):
            new_member.createdByMember = new_member.uuid
            new_member.updatedByMember = new_member.uuid
            db.commit()
            db.refresh(new_member)

        return created_response(
            "Member created successfully"
        )

    except HTTPException as http_exc:
        raise http_exc

    except SQLAlchemyError as db_err:
        db.rollback()
        logging.exception("Database error during registration:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "A database error occurred. Please try again later.",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

    except Exception as e:
        logging.exception("Unexpected error during registration:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred during registration.",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )

def login(loginEmail: str, password: str, db: Session):
    url = f"https://{settings.DOMAIN}/oauth/token"

    payload = {
        "grant_type": "http://auth0.com/oauth/grant-type/password-realm",
        "username": loginEmail,
        "password": password,
        "audience": settings.AUDIENCE,
        "client_id": settings.CLIENT_ID,
        "client_secret": settings.CLIENT_SECRET,
        "scope": "openid profile email",
        "realm": "Username-Password-Authentication"
    }

    response = requests.post(url, json=payload)
    
    if response.status_code != 200:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                    "message": "Login failed due to invalid credentials",
                    "status_code": status.HTTP_401_UNAUTHORIZED
                }
        )

    access_token = response.json().get("access_token")

    if not access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "message": "Login failed: Access token not received.",
                "status_code": status.HTTP_401_UNAUTHORIZED
            }
        )
        
    # Decode the token to recheck in db
    try:
        decoded = jwt.get_unverified_claims(access_token)
        auth0id = decoded.get("sub")
        
        if not auth0id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Invalid token: 'sub' (Auth0 user ID) is missing.",
                    "status_code": status.HTTP_400_BAD_REQUEST
                }
            )

        existing_user = db.query(CoAuth0User).filter_by(userId=auth0id).first()

        if existing_user:
            member = db.query(CoMember).filter_by(auth0Id=existing_user.userId).first()
            
            if member:
                return success_response(
                    "Login successful",
                    {
                        "uuid": member.uuid,
                        "email": existing_user.email,
                        "token": access_token
                    }
                )
            else:
                raise HTTPException(status_code=404, detail={"message": "Member not found for the given Auth0 user."})

        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "message": "User not found in the database. Please register first.",
                    "status_code": status.HTTP_401_UNAUTHORIZED
                }
            )

    except HTTPException as http_exc:
        raise http_exc

    except Exception as e:
        logging.exception("Unexpected error during login:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": f"Internal server error: {str(e)}",
                "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR
            }
        )
        
def login_redirect(provider: str, redirect_uri: str):
    base_url = f"https://{settings.DOMAIN}/authorize"
    params = {
        "client_id": settings.CLIENT_ID,
        "response_type": "code",
        "redirect_uri": redirect_uri,
        "scope": settings.SCOPE,
        "audience": settings.AUDIENCE,
    }

    if provider.lower() == 'google':
        params["connection"] = "google-oauth2"
    elif provider.lower() == 'apple':
        params["connection"] = "apple"
    elif provider.lower() == 'linkedin':
        params["connection"] = "linkedin"

    url = f"{base_url}?{requests.compat.urlencode(params)}"
    return {
        "status_code": status.HTTP_200_OK,
        "success": True,
        "url": url
    }
        
def callback(code: str, db: Session):
    if not code:
        raise HTTPException(status_code=400, detail="Missing code")

    token_url = f"https://{settings.DOMAIN}/oauth/token"

    payload = {
        "grant_type": "authorization_code",
        "client_id": settings.CLIENT_ID,
        "client_secret": settings.CLIENT_SECRET,
        "code": code,
        "redirect_uri": settings.REDIRECT_URI
    }

    headers = { "Content-Type": "application/json" }
    response = requests.post(token_url, json=payload, headers=headers)
    data = response.json()

    id_token = data.get("id_token")
    try:
        decoded = jwt.get_unverified_claims(id_token)
        user_sub = decoded.get("sub")
        
        new_member = None      
        existing_member = db.query(CoMember).filter_by(auth0Id=user_sub).first()
        
        if existing_member is None:
            new_member_auth0_users = CoAuth0User(
                userId=user_sub,
                email=decoded.get("email"),
                name=decoded.get("name"),
                familyName=decoded.get("familyName"),
                givenName=decoded.get("given_name"),
                nickName=decoded.get("nickname"),
                picture=decoded.get("picture"),
                emailVerified=decoded.get("email_verified"),
                identities=decoded.get("identities"),
                createdAt=decoded.get("created_at"),
                updatedAt=decoded.get("updated_at")
            )
            new_member = CoMember(
                auth0Id=user_sub,
                loginEmail=decoded.get("email")
            )
            
            db.add(new_member)
            db.add(new_member_auth0_users)
            db.commit()
            db.refresh(new_member)
            db.refresh(new_member_auth0_users)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to decode id_token: {str(e)}")

    member = existing_member or new_member
    return success_response(
            "Login successful",
            {
                "uuid": member.uuid if member else None,
                "email": member.loginEmail if member else None,
                "token": data["access_token"]
            }
        )

def register_user_in_auth0(email: str, password: str, admin_user_payload: dict):
    domain = settings.DOMAIN
    token = get_management_token()
    
    
    if not domain or not token:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Auth0 domain or token is not configured properly."
        )
    auth0 = Auth0(domain, token)

    try:
        new_user = auth0.users.create({
            "email": email,
            "password": password,
            "connection": "Username-Password-Authentication"
        })
        
        if admin_user_payload.get("sub"):
            auth0.users.update(
                id=new_user["user_id"],
                body={"email_verified": True}
            )
        else:
            pass
        

    except Exception as e:
        raise HTTPException(status_code=409, detail=str(e))

    return {
        "new_user": new_user,
        "token": token
    }